import clsx from 'clsx'
import React, {
  CSSProperties,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import Text from '../Text'
import './styles.css'

const inputClassNameObj = {
  prefixSuffixClassName: 'flex text-Primary-Color text-subBody',
  disabledPrefixSuffixClassName: '!text-Disable-Text',

  inputClassName:
    'w-full focus:placeholder:text-transparent placeholder:text-Placeholder-Text text-Primary-Color text-subBody bg-Input-Field !outline-0 !ring-transparent',
  inputDisabledClassName: '!text-Disable-Text',
  inputStyleClassName:
    // eslint-disable-next-line max-len
    'h-[34px] px-[12px] py-[8px] bg-Input-Field border border-border-base-icon rounded-[8px] hover:border-transparent hover:bg-Input-Main-03 hover:bg-origin-border hover-bg-clip-padding-border focus:border-transparent focus:bg-Input-Main-03 focus:bg-origin-border focus-bg-clip-padding-border focus:shadow-focus',
  inputHoverClassName:
    'border-transparent bg-Input-Main-03 bg-origin-border bg-clip-padding-border shadow-focus',
  inputErrorClassName: '!border-Error-Color hover:!border-Error-Color',
  inputDisabledStyleClassName:
    '!border-Disable hover:!border-Disable !text-Disable-Text !placeholder:text-Disable-Text',

  subTextClassName: 'px-[4px] flex items-center text-supportText',
}

export default function Input({
  onFocus,
  onBlur,
  onPressEnter,
  onKeyDown,
  onChange,
  className,
  id,
  value,
  placeholder,

  subTextClassName,
  helperId,
  helperText,
  errorId,
  isError = false,
  errorText,

  labelClassName,
  label,
  // size,
  prefix,
  suffix,
  activePrefix = prefix,
  activeSuffix = suffix,
  secure,
  disabled,
  minLength,
  maxLength = 255,
  type,
  isFullWidth = false,
  autoFocus,
  classNameInputWrapper = '',
  classNameInput = '',
  required = false,
}: InputProps) {
  const { t } = useTranslation()
  const inputReference = useRef<any>(null)
  const [isMouseOver, setIsMouseOver] = useState(false)
  const [focused, setFocused] = useState(false)
  const [isEmpty, setIsEmpty] = useState(false)

  const inputClassName = useMemo(
    () =>
      clsx(
        `${inputClassNameObj.inputClassName}`,
        {
          [inputClassNameObj.inputDisabledClassName]: disabled,
        },
        prefix || suffix ? '' : className
      ),
    [className, disabled, prefix, suffix]
  )

  const inputStyleClassName = (() => {
    if (prefix || suffix) {
      return clsx(
        `flex w-full items-center gap-[12px] ${inputClassNameObj.inputStyleClassName}`,
        {
          [inputClassNameObj.inputHoverClassName]: focused,
          [inputClassNameObj.inputDisabledStyleClassName]: disabled,
          [inputClassNameObj.inputErrorClassName]: isError || isEmpty,
          [className ?? '']: className,
        }
      )
    }

    return clsx(
      `${inputClassNameObj.inputClassName} ${inputClassNameObj.inputStyleClassName}`,
      {
        [inputClassNameObj.inputDisabledStyleClassName]: disabled,
        [inputClassNameObj.inputErrorClassName]: isError || isEmpty,
        [className ?? '']: className,
      }
    )
  })()

  const prefixSuffixClassName = useMemo(
    () =>
      clsx(inputClassNameObj.prefixSuffixClassName, {
        [inputClassNameObj.disabledPrefixSuffixClassName]: disabled,
      }),
    [disabled]
  )

  const inputStyle = useMemo(() => {
    const inputStyleFinal: CSSProperties | any = {}
    if (secure) {
      inputStyleFinal.WebkitTextSecurity = 'disc'
    }

    return inputStyleFinal
  }, [secure])

  const onClickBlock = useCallback(() => inputReference.current.focus(), [])
  const onFocusBlock = useCallback(
    (e: any) => {
      e.preventDefault()

      onFocus?.(e)
      setFocused(true)
    },
    [onFocus]
  )

  const onBlurBlock = useCallback(
    (e: any) => {
      if (!isMouseOver) {
        e.preventDefault()

        // Check if field is required and empty
        if (required && (!value || value.toString().trim() === '')) {
          setIsEmpty(true)
        } else {
          setIsEmpty(false)
        }

        onBlur?.(e)
        setFocused(false)
      }
    },
    [isMouseOver, onBlur, required, value]
  )

  const handleKeyDown = useCallback(
    (event: any) => {
      onKeyDown?.(event)
      if (event.key === 'Enter') {
        onPressEnter?.(event)
      }
    },
    [onKeyDown, onPressEnter]
  )

  const handleMouseEnter = useCallback(() => {
    setIsMouseOver(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsMouseOver(false)
  }, [])

  const input = useMemo(() => {
    if (prefix || suffix) {
      return (
        <div
          className={inputStyleClassName}
          onClick={onClickBlock}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {prefix && (
            <div className={prefixSuffixClassName}>
              {focused ? activePrefix : prefix}
            </div>
          )}
          <input
            ref={inputReference}
            id={id}
            className={clsx(classNameInput, inputClassName)}
            style={inputStyle}
            value={value}
            placeholder={placeholder}
            type={type}
            disabled={disabled}
            minLength={minLength}
            maxLength={maxLength}
            autoFocus={autoFocus}
            onFocus={onFocusBlock}
            onBlur={onBlurBlock}
            onChange={onChange}
            onKeyDown={handleKeyDown}
          />
          {suffix && (
            <div className={prefixSuffixClassName}>
              {focused ? activeSuffix : suffix}
            </div>
          )}
        </div>
      )
    }

    return (
      <input
        ref={inputReference}
        id={id}
        className={inputStyleClassName}
        style={inputStyle}
        value={value}
        placeholder={placeholder}
        type={type}
        disabled={disabled}
        minLength={minLength}
        maxLength={maxLength}
        autoFocus={autoFocus}
        onFocus={onFocusBlock}
        onBlur={onBlurBlock}
        onChange={onChange}
        onKeyDown={handleKeyDown}
      />
    )
  }, [
    focused,
    id,
    value,
    placeholder,
    type,
    prefix,
    suffix,
    activePrefix,
    activeSuffix,
    disabled,
    minLength,
    maxLength,
    autoFocus,
    inputStyle,
    inputClassName,
    inputStyleClassName,
    prefixSuffixClassName,
    onClickBlock,
    onFocus,
    onBlur,
    onFocusBlock,
    onBlurBlock,
    onChange,
    handleKeyDown,
    isError,
    classNameInput,
  ])

  const subTextClassNameFinal = useMemo(
    () =>
      clsx(inputClassNameObj.subTextClassName, subTextClassName, {
        'text-Secondary-Color': helperText && !isEmpty,
        '!text-Error-Color': isError || isEmpty,
      }),
    [helperText, isError, subTextClassName, isEmpty]
  )

  const subTextContent = useMemo(() => {
    if (isEmpty) {
      return t('common.required')
    }
    if (errorText) {
      return errorText
    }
    return helperText
  }, [helperText, errorText, isEmpty, t])

  const subTextId = useMemo(() => {
    if (isEmpty || errorText) {
      return errorId
    }
    return helperId
  }, [errorText, helperId, errorId, isEmpty])

  return (
    <div
      className={clsx(
        classNameInputWrapper,
        'flex flex-col gap-[4px]',
        isFullWidth && 'w-full'
      )}
    >
      {label && (
        <Text
          className={clsx('flex px-1 text-Tertiary-Color', labelClassName)}
          variant="medium"
          type="subBody"
        >
          {label}
          {required && <span className="ml-1">*</span>}
        </Text>
      )}
      <div className="relative flex flex-col gap-[4px]">
        {input}
        {subTextContent && (
          <div id={subTextId} className={subTextClassNameFinal}>
            {subTextContent}
          </div>
        )}
      </div>
    </div>
  )
}

export interface InputRef {
  blur: () => void
  focus: () => void
}

export const InputWithRef = forwardRef<InputRef, InputProps>(
  (
    {
      onFocus,
      onBlur,
      onPressEnter,
      onChange,
      className,
      id,
      value,
      placeholder,

      subTextClassName,
      helperId,
      helperText,
      errorId,
      isError = false,
      errorText,

      labelClassName,
      label,
      // size,
      prefix,
      suffix,
      activePrefix = prefix,
      activeSuffix = suffix,
      secure,
      disabled,
      minLength,
      maxLength = 255,
      type,
      isFullWidth = false,
      autoFocus,
      classNameInputWrapper = '',
      classNameInput = '',
      required = false,
    }: InputProps,
    ref
  ) => {
    const { t } = useTranslation()
    const inputReference = useRef<any>(null)
    const [isMouseOver, setIsMouseOver] = useState(false)
    const [focused, setFocused] = useState(false)
    const [isEmpty, setIsEmpty] = useState(false)

    useImperativeHandle(ref, () => ({
      blur: () => {
        inputReference.current?.blur()

        onBlur?.({ preventDefault: () => {} })
        setFocused(false)
      },
      focus: () => inputReference.current?.focus(),
    }))

    const inputClassName = useMemo(
      () =>
        clsx(`${inputClassNameObj.inputClassName}`, {
          [inputClassNameObj.inputDisabledClassName]: disabled,
          [className ?? '']: className,
        }),
      [className, disabled]
    )

    const inputStyleClassName = (() => {
      if (prefix || suffix) {
        return clsx(
          `flex w-full items-center gap-[12px] ${inputClassNameObj.inputStyleClassName}`,
          {
            [inputClassNameObj.inputHoverClassName]: focused,
            [inputClassNameObj.inputDisabledStyleClassName]: disabled,
            [inputClassNameObj.inputErrorClassName]: isError || isEmpty,
            [className ?? '']: className,
          }
        )
      }

      return clsx(
        `${inputClassNameObj.inputClassName} ${inputClassNameObj.inputStyleClassName}`,
        {
          [inputClassNameObj.inputDisabledStyleClassName]: disabled,
          [inputClassNameObj.inputErrorClassName]: isError || isEmpty,
          [className ?? '']: className,
        }
      )
    })()

    const prefixSuffixClassName = useMemo(
      () =>
        clsx(inputClassNameObj.prefixSuffixClassName, {
          [inputClassNameObj.disabledPrefixSuffixClassName]: disabled,
        }),
      [disabled]
    )

    const inputStyle = useMemo(() => {
      const inputStyleFinal: CSSProperties | any = {}
      if (secure) {
        inputStyleFinal.WebkitTextSecurity = 'disc'
      }

      return inputStyleFinal
    }, [secure])

    const onClickBlock = useCallback(() => inputReference.current.focus(), [])
    const onFocusBlock = useCallback(
      (e: any) => {
        e.preventDefault()

        onFocus?.(e)
        setFocused(true)
      },
      [onFocus]
    )

    const onBlurBlock = useCallback(
      (e: any) => {
        if (!isMouseOver) {
          e.preventDefault()

          // Check if field is required and empty
          if (required && (!value || value.toString().trim() === '')) {
            setIsEmpty(true)
          } else {
            setIsEmpty(false)
          }

          onBlur?.(e)
          setFocused(false)
        }
      },
      [isMouseOver, onBlur, required, value]
    )

    const handleKeyDown = useCallback(
      (event: any) => {
        if (event.key === 'Enter') {
          onPressEnter?.(event)
        }
      },
      [onPressEnter]
    )

    const handleMouseEnter = useCallback(() => {
      setIsMouseOver(true)
    }, [])

    const handleMouseLeave = useCallback(() => {
      setIsMouseOver(false)
    }, [])

    const input = useMemo(() => {
      if (prefix || suffix) {
        return (
          <div
            className={inputStyleClassName}
            onClick={onClickBlock}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {prefix && (
              <div className={prefixSuffixClassName}>
                {focused ? activePrefix : prefix}
              </div>
            )}
            <input
              ref={inputReference}
              id={id}
              className={clsx(classNameInput, inputClassName)}
              style={inputStyle}
              value={value}
              placeholder={placeholder}
              type={type}
              disabled={disabled}
              minLength={minLength}
              maxLength={maxLength}
              autoFocus={autoFocus}
              onFocus={onFocusBlock}
              onBlur={onBlurBlock}
              onChange={onChange}
              onKeyDown={handleKeyDown}
            />
            {suffix && (
              <div className={prefixSuffixClassName}>
                {focused ? activeSuffix : suffix}
              </div>
            )}
          </div>
        )
      }

      return (
        <input
          ref={inputReference}
          id={id}
          className={inputStyleClassName}
          style={inputStyle}
          value={value}
          placeholder={placeholder}
          type={type}
          disabled={disabled}
          minLength={minLength}
          maxLength={maxLength}
          autoFocus={autoFocus}
          onFocus={onFocusBlock}
          onBlur={onBlurBlock}
          onChange={onChange}
          onKeyDown={handleKeyDown}
        />
      )
    }, [
      focused,
      id,
      value,
      placeholder,
      type,
      prefix,
      suffix,
      activePrefix,
      activeSuffix,
      disabled,
      minLength,
      maxLength,
      autoFocus,
      inputStyle,
      inputClassName,
      inputStyleClassName,
      prefixSuffixClassName,
      onClickBlock,
      onFocus,
      onBlur,
      onFocusBlock,
      onBlurBlock,
      onChange,
      handleKeyDown,
      isError,
      classNameInput,
    ])

    const subTextClassNameFinal = useMemo(
      () =>
        clsx(inputClassNameObj.subTextClassName, subTextClassName, {
          'text-Secondary-Color': helperText && !isEmpty,
          '!text-Error-Color': isError || isEmpty,
        }),
      [helperText, isError, subTextClassName, isEmpty]
    )

    const subTextContent = useMemo(() => {
      if (isEmpty) {
        return t('common.required')
      }
      if (errorText) {
        return errorText
      }
      return helperText
    }, [helperText, errorText, isEmpty, t])

    const subTextId = useMemo(() => {
      if (isEmpty || errorText) {
        return errorId
      }
      return helperId
    }, [errorText, helperId, errorId, isEmpty])

    return (
      <div
        className={clsx(
          classNameInputWrapper,
          'flex flex-col gap-[4px]',
          isFullWidth && 'w-full'
        )}
      >
        {label && (
          <Text
            className={clsx('flex px-1 text-Tertiary-Color', labelClassName)}
            variant="medium"
            type="subBody"
          >
            {label}
            {required && <span className="ml-1">*</span>}
          </Text>
        )}
        <div className="relative flex flex-col gap-[4px]">
          {input}
          {subTextContent && (
            <div id={subTextId} className={subTextClassNameFinal}>
              {subTextContent}
            </div>
          )}
        </div>
      </div>
    )
  }
)

const reEmail: RegExp =
  /^(([^<>()[\]\\.,;:\s@\\"]+(\.[^<>()[\]\\.,;:\s@\\"]+)*)|(\\".+\\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

const EmailInput = ({
  onValidate,
  onChange,
  onBlur,
  onFocus,
  onPressEnter,
  value,
  showErrorText,
  errorText,
  isError,

  ...rest
}: InputProps | any) => {
  const [showError, setShowError] = useState(false)
  const [errorTextFinal, setErrorTextFinal] = useState<any>()
  const [isFocus, setIsFocus] = useState(false)
  const [isEnter, setIsEnter] = useState(false)
  const { t } = useTranslation()

  const getErrorText = useCallback(() => {
    if (showErrorText) {
      setErrorTextFinal(errorText)
    } else if (showError) {
      if (!value) {
        setErrorTextFinal(undefined)
      } else {
        setErrorTextFinal(t('email.invalid_email'))
      }
    } else {
      setErrorTextFinal(undefined)
    }
  }, [showError, value, errorText, showErrorText, t])

  const validateEmail = useCallback(
    (email: string | undefined) => email?.trim()?.match?.(reEmail),
    []
  )

  const handleBlur = useCallback(
    (e: any) => {
      setIsEnter(false)
      setIsFocus(false)
      onBlur?.(e)
      setShowError(!validateEmail(value))
    },
    [value, onBlur]
  )

  const handleFocus = useCallback(
    (e: any) => {
      onFocus?.(e)
    },
    [onFocus]
  )

  const handleChange = useCallback(
    (e: any) => {
      setIsEnter(false)
      onChange?.(e)
      onValidate?.(!!validateEmail(e.target.value))
      setShowError(false)
    },
    [onValidate, onChange]
  )

  const handlePressEnter = useCallback(
    (e: any) => {
      setIsEnter(true)
      onPressEnter?.(e)
      setShowError(!validateEmail(value))
    },
    [value, onPressEnter]
  )

  useEffect(() => {
    if (!isFocus || isEnter) {
      getErrorText()
    }
  }, [isFocus, isEnter, showError, value, errorText, showErrorText])

  return (
    <Input
      value={value}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onChange={handleChange}
      onPressEnter={handlePressEnter}
      errorText={errorTextFinal}
      isError={errorTextFinal || isError}
      {...rest}
    />
  )
}

Input.Email = EmailInput

export interface InputProps {
  onFocus?: (e: any) => void
  onBlur?: (e: any) => void
  onPressEnter?: (e: any) => void
  onKeyDown?: (e: any) => void
  onChange?: (e: any) => void
  className?: string
  id?: string | undefined
  value?: string
  placeholder?: string
  type?: string

  subTextClassName?: string
  helperId?: string
  helperText?: string | React.ReactNode
  errorId?: string
  errorText?: string | React.ReactNode
  isError?: boolean

  labelClassName?: string
  label?: string | React.ReactNode
  size?: 'small' | 'medium' | 'large'
  prefix?: React.ReactNode
  suffix?: React.ReactNode
  activePrefix?: React.ReactNode
  activeSuffix?: React.ReactNode
  secure?: boolean
  disabled?: boolean
  minLength?: number | undefined
  maxLength?: number | undefined
  isFullWidth?: boolean
  autoFocus?: boolean
  classNameInputWrapper?: string
  classNameInput?: string
  required?: boolean
}
