import React, { useState } from 'react'
import Input, { InputWithRef } from './index'

// Test component để kiểm tra validation onBlur
export default function TestInputValidation() {
  const [value1, setValue1] = useState('')
  const [value2, setValue2] = useState('')
  const [value3, setValue3] = useState('')

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-2xl font-bold">Test Input Validation onBlur</h1>
      
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Input Component (Required)</h2>
        <Input
          label="Tên người dùng"
          placeholder="Nhập tên người dùng"
          value={value1}
          onChange={(e) => setValue1(e.target.value)}
          required={true}
        />
        
        <h2 className="text-lg font-semibold">Input Component với Prefix (Required)</h2>
        <Input
          label="Email"
          placeholder="Nhập email"
          value={value2}
          onChange={(e) => setValue2(e.target.value)}
          required={true}
          prefix={<span>@</span>}
        />
        
        <h2 className="text-lg font-semibold">InputWithRef Component (Required)</h2>
        <InputWithRef
          label="Mật khẩu"
          placeholder="Nhập mật khẩu"
          value={value3}
          onChange={(e) => setValue3(e.target.value)}
          required={true}
          type="password"
        />
        
        <h2 className="text-lg font-semibold">Input Component (Not Required)</h2>
        <Input
          label="Ghi chú (tùy chọn)"
          placeholder="Nhập ghi chú"
          helperText="Trường này không bắt buộc"
        />
      </div>
      
      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold mb-2">Hướng dẫn test:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>Click vào các input có label có dấu * (required)</li>
          <li>Để trống và click ra ngoài (onBlur)</li>
          <li>Sẽ thấy border đỏ và text lỗi "Required" xuất hiện</li>
          <li>Nhập nội dung vào input và blur lại để thấy lỗi biến mất</li>
        </ul>
      </div>
    </div>
  )
}
