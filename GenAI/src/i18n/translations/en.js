export default {
  'add_tool.buy_tool': 'Buy tool?',
  'add_tool.buy_tool_success': 'Successfully bought tool',
  'add_tool.buy_tool.description':
    'If continue, this tool will be successfully acquired and added to your Tool Collection.',
  'add_tool.get_it_free': 'Get it free',
  'add_tool.my_tools': 'My tools',
  'add_tool.required_params': 'Built-in tool requiring parameter configuration',
  'add_tool.search_tool_name': 'Search by tool name',
  'add_tool.title': 'Add toolkit',
  'ai_foundation.sub_title':
    'Empower worker to be more intelligent, responsive, and capable in a wide range of applications.',
  'category.search_category_placeholder':
    'Select category or type in to search',
  'chat_embed.code': 'Code',
  'chat_embed.continue': 'Continue',
  'chat_embed.continue_incognito': 'Continue incognito',
  'chat_embed.header_subtitle': 'Tell me what you need. I am here to help!',
  'chat_embed.hello_message': 'Hi, I am your omnipotent virtual assistant!',
  'chat_embed.hello_message_2':
    'Hi, I am your omnipotent virtual assistant!\nEnter your email to sign in',
  'chat_embed.hello_message_3':
    '6-digit code sent to your email, type in verification code here to continue',
  'chat_embed.placeholder': 'Send message...',
  'chat_embed.sign': 'Powered by',
  'chat_embed.start': 'Start',
  'citation_message.suggestions': 'Suggestions',
  'citation.format_not_support.text1': 'Could you review this citation on',
  'citation.format_not_support.text2': 'page {{page}}',
  'citation.format_not_support.text3': 'of your document file',
  'citation.format_not_support.title': 'Format is currently not supported!',
  'citation.hi': 'Hi',
  'citation.tell_me_what_you_need':
    'Tell me the information you need, I’ll find the citation for you!',
  'common.add': 'Add',
  'common.add_all': 'Add all',
  'common.added': 'Added',
  'common.added_all': 'Added all',
  'common.all': 'All',
  'common.back': 'Back',
  'common.cancel': 'Cancel',
  'common.category': 'Category',
  'common.close': 'Close',
  'common.collapse': 'Collapse',
  'common.confirm': 'Confirm',
  'common.connect': 'Connect',
  'common.copied': 'Copied!',
  'common.copy': 'Copy',
  'common.copyright': 'Copyright',
  'common.delete': 'Delete',
  'common.description': 'Description',
  'common.email_address': 'Email address',
  'common.empty': 'Empty',
  'common.end': 'End',
  'common.end_date': 'End date',
  'common.expand': 'Expand',
  'common.free': 'Free',
  'common.logout': 'Logout',
  'common.more': 'More',
  'common.more_count': '{{count}} more',
  'common.new_chat': 'New chat',
  'common.no_data_found': 'No Data Found',
  'common.no_data_found.sub_text':
    'Oops, we couldn’t find what you are looking for\nPlease try again and remember to check spelling!',
  'common.ok': 'OK',
  'common.online_search': 'Online search',
  'common.oops': 'Oops!',
  'common.optional': '(Optional)',
  'common.page': 'Page',
  'common.page_of_pages': '{{page}} of {{totalPage}}',
  'common.plan_usage': 'Plan usage',
  'common.plans_pricing': 'Plans & Pricing',
  'common.playground': 'Playground',
  'common.privacy': 'Privacy',
  'common.re_generate': 'Re-generate token',
  'common.re_generated': 'Re-generated token',
  'common.remaining_storage': 'Remaining storage',
  'common.remaining_token': 'Remaining token',
  'common.remove': 'Remove',
  'common.remove_all': 'Remove all',
  'common.results': 'Results',
  'common.retry': 'Retry',
  'common.run': 'Run',
  'common.save': 'Save',
  'common.search_by_keyword': 'Search by keywords',
  'common.select': 'Select here',
  'common.select_all': 'Select all',
  'common.selected': 'Selected',
  'common.session_expired.description':
    'Please log in again to continue accessing the system',
  'common.session_expired.title': 'Your session has expired!',
  'common.settings': 'Settings',
  'common.something_went_wrong': 'Something went wrong',
  'common.sorry_an_unexpected_error_has_occurred':
    'Sorry, an unexpected error has occurred.',
  'common.start': 'Start',
  'common.start_date': 'Start date',
  'common.storage_capacity': 'Storage capacity',
  'common.system': 'System',
  'common.terms': 'Terms',
  'common.token': 'Token',
  'common.tools': 'Tools',
  'common.uncategorized': 'Uncategorized',
  'common.unselect_all': 'Unselect all',
  'common.update': 'Update',
  'common.upgrade': 'Upgrade',
  'common.when_to_use': 'When to use',
  'common.workers': 'Workers',
  'dashboard.anonymous_user': 'Anonymous user',
  'dashboard.chat_widget': 'Chat Widget',
  'dashboard.last_updated': 'Last updated',
  'dashboard.latest': 'latest',
  'dashboard.messenger': 'Messenger',
  'dashboard.no_session_found':
    'None of your customers have reached out to you',
  'dashboard.overview': 'Overview',
  'dashboard.platform': 'Platform',
  'dashboard.refresh_data': 'Refresh data',
  'dashboard.search_by_name_or_id': 'Search by name or ID',
  'dashboard.session': 'Session',
  'dashboard.session_log': 'Session Log',
  'dashboard.sessions': 'Sessions',
  'dashboard.telegram': 'Telegram',
  'dashboard.token_used': 'Token used',
  'dashboard.total': 'Total',
  'dashboard.whatsapp': 'WhatsApp',
  'dashboard.workflow': 'Workflow',
  'dashboard.zalo': 'Zalo',
  'dialog.action_required': 'Action required',
  'dialog.confirmation': 'Confirmation',
  'dialog.error': 'Error',
  'dialog.publish_workflow': 'Publish your tool to proceed',
  'dialog.publish_workflow.description':
    'This workflow contains unpublished Tools. Please go to your Tool Collection and publish them all',
  'dialog.wanna_leave.description':
    'If continue, your changes may not be saved',
  'dialog.wanna_leave.title': 'Wanna leave?',
  'dialog.warning': 'Warning',
  'email.invalid_email': 'Invalid email address',
  'empty_data.empty': 'Empty',
  'empty_data.no_available_items': 'No available items here',
  'error_page.forbidden': 'FORBIDDEN',
  'error_page.go_back': 'Go back',
  'error_page.go_home': 'Go home',
  'error_page.sorry_we_could_not_find_the_space_you_are_looking_for':
    'Sorry! We couldn’t find the space you’re looking for',
  'error_page.you_are_lost': 'You’re lost',
  'error_page.you_do_not_have_the_permission_to_access_this_page':
    'You do not have the permission to access this page',
  'extension_utilities.an_extension_application_designed_to_streamline_and_enhance_your_email_writing_process':
    'An extension application designed to streamline and enhance your email writing process.',
  'extension_utilities.extension': 'Extension',
  'extension_utilities.get_quick_accurate_answers_with_relevant_references_to_enhance_your_research_efficiency':
    'Get quick, accurate answers with relevant references to enhance your research efficiency',
  'extension_utilities.linkedin_content_creator': 'Linkedin Content Creator',
  'extension_utilities.outlook_email_assistant': 'Outlook Email Assistant',
  'extension_utilities.provides_personalized_writing_suggestions_and_optimizes_your_posts_for_greater_engagement':
    'Provides personalized writing suggestions and optimizes your posts for greater engagement',
  'extension_utilities.universal_chat': 'Universal Chat',
  'extension_utilities.web_application': 'Web application',
  'file_select.description': 'Description',
  'file_select.format_is_not_supported': 'Format is not supported',
  'file_select.max_size': 'Max size {{size}}MB',
  'file_select.upload_file': 'Upload file',
  'history.delete_all_conversation.title': 'Delete all conversations?',
  'history.delete_conversation.description': 'This action cannot be undone',
  'history.delete_conversation.title': 'Delete this conversation?',
  'history.no_history_yet': 'No history yet',
  'home.answer_with_internet_search': 'Answer with internet search',
  'home.answer_without_internet_search': 'Answer without internet search',
  'home.attach_file.tooltip':
    'Max 200MB, support jpg, png, csv, pdf, txt, docx',
  'home.attach_file.tooltip_disabled':
    'Attachments disabled for Web search mode',
  'home.chat_history': 'Chat History',
  'home.file_max_size': 'Max {{size}}MB',
  'home.history': 'History',
  'home.not_supported': 'Not supported',
  'home.placeholder_chat': 'Ask anything',
  'home.search': 'Search',
  'home.search_workflow': 'Search workflow',
  'home.sub_welcome_message': 'How can I assist you today?',
  'home.web_search': 'Web search',
  'home.welcome_message': 'Hi,',
  'intent.extract_information': 'Extract information',
  'intent.query_and_search': 'Query and search',
  'intent.summarize_content': 'Summarize content',
  'knowledge_base.add_data_source_for_your_worker':
    'Add data source for your worker',
  'knowledge_base.add_external_data': 'Add external data',
  'knowledge_base.add_internal_data': 'Add internal data',
  'knowledge_base.add_new_data_source': 'Add new data source',
  'knowledge_base.add_new_directory': 'Add new directory',
  'knowledge_base.add_new_file': 'Add new file',
  'knowledge_base.add_new.description': 'Add data source for your worker',
  'knowledge_base.add_new.title': 'Upload knowledge file',
  'knowledge_base.all_directory': 'Directory',
  'knowledge_base.cannot_connect_to_your_database':
    'Cannot connect to your database, please retry',
  'knowledge_base.click_here_to_create_new_external_source':
    'Click here to create new external source',
  'knowledge_base.completely_processed_file': 'Completely processed file',
  'knowledge_base.connect_to_your_database_and_get_all_created_views':
    'Connect to your database and get all created Views',
  'knowledge_base.connection_string_must_be':
    'Connection string must be: {{postgresql}}',
  'knowledge_base.continue': 'Continue',
  'knowledge_base.create_new_directory_to_import_knowledge_file':
    'Create new directory to import knowledge file',
  'knowledge_base.data_access': 'Data access',
  'knowledge_base.data_is_being_synced': 'Data is being synced',
  'knowledge_base.data_source_configuration': 'Data Source Configuration',
  'knowledge_base.delete': 'Delete',
  'knowledge_base.delete_directory': 'Delete directory?',
  'knowledge_base.delete_directory_sub_message':
    'If continue, this directory and all added knowledge files will be permanently removed.',
  'knowledge_base.delete_external_data_source': 'Delete external data source?',
  'knowledge_base.delete_external_data_source_sub_message':
    'If continue, this data source will be permanently removed. Consequently, any workflow steps associated with this data will no longer be able to access.',
  'knowledge_base.delete_knowledge_file': 'Delete knowledge file?',
  'knowledge_base.delete_knowledge_file_sub_message':
    'If continue, this file will be permanently removed. Consequently, any workflow steps associated with this file will no longer be able to access its data.',
  'knowledge_base.description': 'Description',
  'knowledge_base.directory': 'Directory',
  'knowledge_base.display_name': 'Display name',
  'knowledge_base.display_name_is_required': 'Display name is required',
  'knowledge_base.edit': 'Edit',
  'knowledge_base.edit_knowledge_file_information':
    'Edit knowledge file information',
  'knowledge_base.edit_your_directory_to_apply_changes':
    'Edit your directory to apply changes',
  'knowledge_base.establishing_connection': 'Establishing connection...',
  'knowledge_base.external': 'External',
  'knowledge_base.failed_to_connect': 'Failed to connect',
  'knowledge_base.file': 'file',
  'knowledge_base.file_is_being_processed': 'File is being processed',
  'knowledge_base.file_name_already_exists': 'File name already exists',
  'knowledge_base.files': 'files',
  'knowledge_base.format_must_be': 'Format must be',
  'knowledge_base.general': 'General',
  'knowledge_base.internal': 'Internal',
  'knowledge_base.it_seems_like_your_connection_string_got_problem':
    'It seems like your connection string got problem',
  'knowledge_base.knowledge_base': 'Knowledge base',
  'knowledge_base.knowledge_base_description':
    'Essential element for worker as it enhances efficiency and accuracy by providing quick access to relevant information, ensuring consistent and accurate responses.',
  'knowledge_base.limit_exceeded': 'Limit exceeded',
  'knowledge_base.mark_as_sensitive_data': 'Mark as sensitive data',
  'knowledge_base.new_data_source': 'New Data Source',
  'knowledge_base.new_directory': 'New Directory',
  'knowledge_base.new_file': 'New File',
  'knowledge_base.not_enough_storage_available_to_complete':
    'Not enough storage available to complete, please recheck your Subscription Plan!',
  'knowledge_base.processing': 'Processing...',
  'knowledge_base.retry_process': 'Retry process',
  'knowledge_base.search_display_name': 'Search by display name',
  'knowledge_base.search_file_name': 'Search by file name',
  'knowledge_base.sensitive_data_will_not_be_suggested_to_connect_in_workflow_to_avoid_leaking_of_confidential_information':
    'Sensitive data will not be suggested to connect in workflow to avoid leaking of confidential information',
  'knowledge_base.source_connection': 'Source connection',
  'knowledge_base.successfully_created_data_source':
    'Successfully created data source',
  'knowledge_base.successfully_created_knowledge_directory':
    'Successfully created knowledge directory',
  'knowledge_base.successfully_deleted_data_source':
    'Successfully deleted data source',
  'knowledge_base.successfully_deleted_knowledge_directory':
    'Successfully deleted knowledge directory',
  'knowledge_base.successfully_deleted_knowledge_file':
    'Successfully deleted knowledge file',
  'knowledge_base.successfully_updated_data_source':
    'Successfully updated data source',
  'knowledge_base.successfully_updated_knowledge_file':
    'Successfully updated knowledge file',
  'knowledge_base.supplementary_information_to_use_knowledge_file':
    'Supplementary information to use knowledge file',
  'knowledge_base.supplementary_information_to_use_your_data':
    'Supplementary information to use your data',
  'knowledge_base.sync': 'Sync',
  'knowledge_base.syncing_data': 'Syncing data',
  'knowledge_base.type_in_directory_name': 'Type in directory name',
  'knowledge_base.type_in_name_for_your_data_source':
    'Type in name for your data source',
  'knowledge_base.type_in_name_for_your_knowledge_file':
    'Type in name for your knowledge file',
  'knowledge_base.type_in_supplementary_info_for_knowledge_file':
    'Type in supplementary info for knowledge file',
  'knowledge_base.update_directory': 'Update directory',
  'knowledge_base.update_knowledge_file': 'Update knowledge file',
  'knowledge_base.upload': 'Upload',
  'knowledge_base.upload_knowledge_file': 'Upload knowledge file',
  'knowledge_base.uploaded': 'Uploaded',
  'knowledge_base.uploading': 'Uploading...',
  'knowledge_base.when_to_use.description':
    'Describe when your worker needs to query data from knowledge base',
  'login.and': 'and',
  'login.by_continuing_you_agree_to_ai_agencys':
    'By continuing, you agree to AI Agency’s',
  'login.continue': 'Continue',
  'login.continue_with_google': 'Continue with Google',
  'login.email_doesnt_exist': 'Email doesn’t exist',
  'login.enter_email_address': 'Enter email address',
  'login.enter_password': 'Enter password',
  'login.forgot_password': 'Forgot password?',
  'login.havent_got_an_account': 'Haven’t got an account?',
  'login.incorrect_email_or_password': 'Incorrect email or password',
  'login.incorrect_password': 'Incorrect password',
  'login.invalid_password': 'Invalid password',
  'login.login_or_register_with_your_email':
    'Login or register with your email',
  'login.or': 'Or',
  'login.please_enter_email': 'Please enter email',
  'login.please_enter_password': 'Please enter password',
  'login.privacy_policy': 'Privacy Policy.',
  'login.register': 'Register',
  'login.terms_of_service': 'Terms of Service',
  'login.welcome_back': 'Welcome back',
  'login.your_account_is_currently_inactive_please_contact_admin_for_support':
    'Your account is currently inactive. Please contact Admin for support',
  'playground.attach_files': 'Attach files',
  'playground.is_handling': 'is handling',
  'playground.send_mess_to_start': 'Send message to start',
  'playground.start': 'Start',
  'playground.task': 'task',
  'playground.type_in_here': 'Type in here',
  'preview_link.not_available': 'Preview is not available',
  'profile.change_password': 'Change password',
  'profile.delete_account': 'Delete account',
  'profile.email_address': 'Email address',
  'profile.manage_account': 'Manage account',
  'profile.name': 'Name',
  'profile.password': 'Password',
  'profile.permanently_delete_the_account': 'Permanently delete the account',
  'profile.personal_details': 'Personal details',
  'profile.successfully_updated_profile': 'Successfully updated profile',
  'profile.your_profile': 'Your profile',
  'publish_workflow.message_publish':
    'This workflow contains unpublished Tools. Please go to your Tool Collection and publish them all',
  'publish_workflow.publish': 'Publish',
  'publish_workflow.subTitle':
    'Choose the appropriate business category to publish your workflow',
  'publish_workflow.success': 'Successfully published workflow template',
  'register.1_number': '1 number',
  'register.1_special_character': '1 special character',
  'register.1_uppercase_letter': '1 uppercase letter',
  'register.6_digit_one_time_password_is_required':
    '6 digit one-time-password is required',
  'register.and': 'and',
  'register.at_least_8_characters_long': 'At least 8 characters long',
  'register.back_to_login': 'Back to login',
  'register.by_creating_an_account_you_agree_to_our_terms_of_services_and_privacy_policy':
    'By creating an account, you agree to our Terms of Services and Privacy Policy',
  'register.continue': 'Continue',
  'register.create_your_profile': 'Create your profile',
  'register.didnt_get_the_code': 'Didn’t get the code?',
  'register.email': 'Email',
  'register.follow_instruction_to_set_your_new_password':
    'Follow instruction to set your new password',
  'register.go_to_login': 'Go to Login',
  'register.had_an_account': 'Had an account?',
  'register.invalid_email_address': 'Invalid email address',
  'register.new_password': 'New password',
  'register.otp_is_invalid_or_expired': 'OTP is invalid or expired',
  'register.password_need_to_be_in_required_format':
    'Password need to be in required format',
  'register.ready_to_optimize_your_business_process_with_our_ai_agency':
    'Ready to optimize your business process with our AI agency?',
  'register.recover_your_account': 'Recover your account',
  'register.resend': 'Resend',
  'register.resend_in': 'Resend in',
  'register.resending': 'Resending...',
  'register.set_password': 'Set password',
  'register.sign_up': 'Sign Up',
  'register.something_went_wrong': 'Something went wrong, please retry!',
  'register.tell_us_about_yourself': 'Tell us about yourself',
  'register.the_otp_will_expired_after_2_minutes':
    'The OTP will expired after 2 minutes',
  'register.this_email_is_already_registered':
    'This email is already registered',
  'register.token_is_invalid_or_expired': 'Token is invalid or expired',
  'register.type_in_new_password': 'Type in new password',
  'register.type_in_otp_you_got_via_email': 'Type in OTP you got via email',
  'register.type_in_your_email_address': 'Type in your email address',
  'register.type_in_your_preferred_name': 'Type in your preferred name',
  'register.verification_code': 'Verification code',
  'register.we_sent_a_6_digit_code_to': 'We sent a 6-digit code to',
  'register.welcome_to_ai_agency': 'Welcome to AI Agency!',
  'register.your_name': 'Your name',
  'reset_password.back_to_login': 'Back to login',
  'reset_password.continue': 'Continue',
  'reset_password.done': 'Done!',
  'reset_password.enter_your_registered_email_we_ll_help_you_to_reset_your_password':
    'Enter your registered email, we’ll help you to reset your password',
  'reset_password.go_to_login': 'Go to Login',
  'reset_password.mistake': 'Mistake?',
  'reset_password.new_password_has_been_set_login_now':
    'New password has been set, login now!',
  'reset_password.recover_your_account': 'Recover your account',
  'reset_password.type_in_your_email_address': 'Type in your email address',
  'sidebar.ai_foundation': 'AI Foundation',
  'sidebar.applications': 'Applications',
  'sidebar.copyright': 'Copyright',
  'sidebar.dashboard': 'Dashboard',
  'sidebar.home': 'Home',
  'sidebar.knowledge_base': 'Knowledge Base',
  'sidebar.main': 'Main',
  'sidebar.marketplace': 'Marketplace',
  'sidebar.my_workflows': 'Workflows',
  'sidebar.others': 'Others',
  'sidebar.pricing': 'Pricing',
  'sidebar.privacy': 'Privacy',
  'sidebar.studio': 'Studio',
  'sidebar.terms': 'Terms',
  'sidebar.tool_category': 'Tool Category',
  'sidebar.tool_collection': 'Tools',
  main: 'Main',
  studio: 'Studio',
  others: 'Others',
  'sidebar.tools': 'Tools',
  'sidebar.workers': 'Workers',
  'sidebar.workflows': 'Workflows',
  'tab_upload.collection': 'Collection',
  'tab_upload.custom': 'Upload',
  'tab_upload.drag_or_click_upload_logo': 'Drag or click to upload logo',
  'tab_upload.maximum_size': 'Maximum size per file 1MB',
  'tab_upload.recommended': 'Recommended size is 100 x 100 pixels',
  'tabs.advanced': 'Advanced',
  'tabs.general': 'General',
  'time.today': 'Today',
  'time.yesterday': 'Yesterday',
  'tool_category.add_new_category': 'Add new category',
  'tool_category.add_new_category_to_sort_out_your_tool_in_marketplace':
    'Add new category to sort out your tool in marketplace',
  'tool_category.cannot_change_default_category':
    'Cannot change default category',
  'tool_category.category': 'Category',
  'tool_category.create_new_category_to_public_tools_in_marketplace':
    'Create new category to public tools in Marketplace',
  'tool_category.delete_tool_category': 'Delete tool category?',
  'tool_category.edit_your_category_to_public_tools_in_marketplace':
    'Edit your category to public tools in Marketplace',
  'tool_category.if_continue_this_category_and_all_tools_in_marketplace_will_be_permanently_removed':
    'If continue, this category and all tools in marketplace will be permanently removed.',
  'tool_category.logo': 'Logo',
  'tool_category.new_category': 'New category',
  'tool_category.new_tool_category': 'New tool category',
  'tool_category.no_tools_added': 'No tools added',
  'tool_category.something_went_wrong': 'Something went wrong!',
  'tool_category.something_wrong_please_retry':
    'Something wrong, please retry!',
  'tool_category.successfully_created_tool_category':
    'Successfully created tool category',
  'tool_category.successfully_deleted_tool_category':
    'Successfully deleted tool category',
  'tool_category.successfully_updated_tool_category':
    'Successfully updated tool category',
  'tool_category.tool_category': 'Tool Category',
  'tool_category.type_in_category_name': 'Type in category name',
  'tool_category.update_tool_category': 'Update tool category',
  'tool_collection.add_new_tool': 'Add new tool',
  'tool_collection.cancel_publishing': 'Cancel publishing',
  'tool_collection.cancel_publishing_tool_on_the_marketplace':
    'Cancel publishing tool on the marketplace?',
  'tool_collection.category': 'Category',
  'tool_collection.change_category': 'Change category',
  'tool_collection.choose_the_appropriate_category_in_the_marketplace_for_adding_your_tool':
    'Choose the appropriate category in the marketplace for adding your tool',
  'tool_collection.click_here_to_create_your_own_tool':
    'Click here to create your own tool',
  'tool_collection.configure_tool_to_support_your_worker_perform_task':
    'Configure tool to support your worker perform task',
  'tool_collection.define_the_functionality_of_the_tool_by_writing_code':
    'This support workers to automate repeated tasks, making them more efficient',
  'tool_collection.delete': 'Delete',
  'tool_collection.delete_tool': 'Delete tool?',
  'tool_collection.description': 'Description',
  'tool_collection.duplicate': 'Duplicate',
  'tool_collection.edit_category': 'Edit category',
  'tool_collection.empty': 'Empty!',
  'tool_collection.enter_default_value_for_parameter':
    'Enter default value for parameter',
  'tool_collection.enter_default_value_for_variable':
    'Enter default value for variable',
  'tool_collection.environment_variable': 'Environment Variable',
  'tool_collection.environment_variables': 'Environment variables',
  'tool_collection.false': 'False',
  'tool_collection.free': 'Free',
  'tool_collection.function': 'Function',
  'tool_collection.if_continue_this_tool_will_be_listed_and_available_for_sale_on_the_marketplace':
    'If continue, this tool will be listed and available for sale on the marketplace.',
  'tool_collection.if_continue_this_tool_will_be_permanently_deleted_and_removed_from_the_market_if_published':
    'If continue, this tool will be permanently deleted and removed from the market (if published).',
  'tool_collection.if_continue_this_tool_will_be_unlisted_and_hide_from_the_marketplace':
    'If continue, this tool will be unlisted and hide from the marketplace.',
  'tool_collection.if_continue_your_changes_may_not_be_saved':
    'If continue, your changes may not be saved',
  'tool_collection.instruction': 'Instruction',
  'tool_collection.no_added_parameters': 'No added parameters',
  'tool_collection.no_added_parameters_and_variables':
    'No added parameters and variables',
  'tool_collection.no_added_variables': 'No added variables',
  'tool_collection.parameter_name': 'Parameter name',
  'tool_collection.provide_instructions_on_how_to_use_this_tool_including_images_for_visualization_is_highly_recommended':
    'Provide instructions on how to use this tool, including images for visualization is highly recommended.',
  'tool_collection.public': 'Public',
  'tool_collection.publish': 'Publish',
  'tool_collection.publish_tool': 'Publish tool',
  'tool_collection.publish_tool_on_the_marketplace':
    'Publish tool on the marketplace?',
  'tool_collection.publishing': 'Publishing...',
  'tool_collection.purchased': 'Purchased',
  'tool_collection.retry': 'Retry',
  'tool_collection.run': 'Run',
  'tool_collection.secret': 'Secret',
  'tool_collection.select_category_or_type_in_to_search':
    'Select category or type in to search',
  'tool_collection.spaces_numbers_special_characters_and_punctuation_are_not_allowed':
    'Spaces, numbers, special characters and punctuation are not allowed',
  'tool_collection.successfully_cancelled_publishing':
    'Successfully cancelled publishing',
  'tool_collection.successfully_deleted_tool': 'Successfully deleted tool',
  'tool_collection.successfully_deleted_tool_category':
    'Successfully deleted tool category',
  'tool_collection.successfully_duplicated_tool':
    'Successfully duplicated tool',
  'tool_collection.successfully_published_tool': 'Successfully published tool',
  'tool_collection.successfully_unlisted_tool': 'Successfully unlisted tool',
  'tool_collection.successfully_updated_tool': 'Successfully updated tool',
  'tool_collection.successfully_updated_tool_category':
    'Successfully updated tool category',
  'tool_collection.successfully_updated_tool_configuration':
    'Successfully updated tool configuration',
  'tool_collection.successfully_updated_tool_configuration_and_instruction':
    'Successfully updated tool configuration and instruction',
  'tool_collection.successfully_updated_tool_instruction':
    'Successfully updated tool instruction',
  'tool_collection.test': 'Test',
  'tool_collection.test_value': 'Test value',
  'tool_collection.the_backbone_of_worker':
    'The backbone of worker, enabling them to perform a wide array of actions, from simple to complex task.',
  'tool_collection.tool_category_has_been_removed_please_retry':
    'Tool category has been removed, please retry',
  'tool_collection.tool_collection': 'Tool Collection',
  'tool_collection.tool_configuration': 'Tool Configuration',
  'tool_collection.tool_name': 'Tool name',
  'tool_collection.tool_name_is_required': 'Tool name is required',
  'tool_collection.tool_name_must_be_in_required_format':
    'Tool name must be in required format',
  'tool_collection.tool_name_must_be_unique': 'Tool name must be unique',
  'tool_collection.tools': 'Tools',
  'tool_collection.true': 'True',
  'tool_collection.type_in_description_for_this_tool':
    'Type in description for this tool',
  'tool_collection.type_in_tool_name': 'Type in tool name',
  'tool_collection.variable': 'Variable',
  'tool_collection.variable_name': 'Variable name',
  'tool_collection.wanna_leave': 'Wanna leave?',
  'tool_marketplace.buy_tool': 'Buy tool?',
  'tool_marketplace.get_it_free': 'Get it free',
  'tool_marketplace.if_continue_this_tool_will_be_successfully_acquired_and_added_to_your_tool_collection':
    'If continue, this tool will be successfully acquired and added to your Tool Collection.',
  'tool_marketplace.instruction': 'Instruction',
  'tool_marketplace.load_more': 'Load more',
  'tool_marketplace.loading': 'Loading...',
  'tool_marketplace.no_instruction': 'No instruction',
  'tool_marketplace.purchase_tools_that_best_suit_your_requirements':
    'Purchase tools that best suit your requirements',
  'tool_marketplace.see_tool_instruction_customize_options':
    'See tool instruction & customize options',
  'tool_marketplace.successfully_bought_tool': 'Successfully bought tool',
  'tool_marketplace.tool_details': 'Tool Details',
  'tool_marketplace.tool_market': 'Tool Market',
  'upload_file.accept_full': 'Only accept {{dataFormat}} format',
  'upload_file.accept_short': 'Only accept {{dataFormat}}',
  'upload_file.drag_and_drop': 'Drag and drop',
  'upload_file.drag_or_click': 'Drag or click to upload',
  'upload_file.failed': 'Failed',
  'upload_file.failed_to_process': 'Failed to process',
  'upload_file.invalid_size': 'Maximum size 10MB',
  'upload_file.invalid_type': 'File type is not supported',
  'upload_file.maximum_full': 'Maximum file size: {{size}}MB',
  'upload_file.maximum_short': 'Maximum size: {{size}}MB',
  'upload_file.support_type_full':
    'Maximum {{size}}MB. Support types: {{dataFormat}}',
  'upload_file.support_type_short': 'Types: {{dataFormat}}',
  'worker_playground.description': 'Run your worker here to test his ability.',
  'workers.add_new': 'Add new',
  'workers.add_new_worker': 'Add new worker',
  'workers.add_system_tag': 'Add system tag',
  'workers.added_system_tag': 'Worker added system tag',
  'workers.ai_worker': 'AI Worker',
  'workers.ai_worker_description':
    'Worker could understand language, learn from data, and take actions to achieve goals automatically',
  'workers.and': 'and ',
  'workers.background': 'Background',
  'workers.choose_type_of_worker_you_want_to_build':
    'Choose type of worker you want to build',
  'workers.click_here_to_create_your_own_worker':
    'Click here to create your own worker',
  'workers.communication_styles': 'Communication Styles',
  'workers.communication_styles_1': 'Communication Styles 1',
  'workers.communication_styles_2': 'Communication Styles 2',
  'workers.communication_styles_3': 'Communication Styles 3',
  'workers.configure_advanced_options': 'Configure advanced options',
  'workers.create_worker': 'Create worker',
  'workers.delete_worker': 'Delete worker?',
  'workers.describe_your_worker_here': 'Describe your worker here...',
  'workers.description':
    'Build your worker to automate tasks, analyze data, manage customer interactions,\npersonalize experiences, monitor performance, and support human.',
  'workers.duplicate': 'Duplicate',
  'workers.enter_playground': 'Playground',
  'workers.exit_playground': 'Exit playground',
  'workers.generate': 'Generate',
  'workers.having_task_let_name_take_care':
    'Having task? Let {{name}} take care!',
  'workers.hello_message_ai_worker_1':
    'I am {{personality}}, I could speak {{language}} and I could assist you to handle task or communicate with you in {{communicationStyle}}',
  'workers.hello_message_ai_worker_2': 'I am {{personality}}',
  'workers.hello_message_ai_worker_3':
    'I am {{personality}}, I could speak {{language}} and I could assist you to handle task',
  'workers.hello_message_ai_worker_4':
    'I could assist you to handle task or communicate with you in {{communicationStyle}}',
  'workers.hi_i_am': 'Hi, I am {{name}}',
  'workers.human_worker': 'Human Worker',
  'workers.human_worker_description':
    'Description for the use of human worker in the workflow',
  'workers.if_continue_this_worker_will_be_permanently_deleted':
    'If continue, please note that all workflow steps associated with this worker will be impacted. You will need to reconfigure them.',
  'workers.just_ask_me_anything_tell_me_that_you_re_ready_by_sending':
    'Just ask me anything, tell me that you\'re ready by sending: "Let\'s start"',
  'workers.language': 'Language',
  'workers.manner_ensuring_that_interactions_are':
    'manner, ensuring that interactions are ',
  'workers.new_chat': 'New chat',
  'workers.new_worker': 'New worker',
  'workers.notes': 'Notes',
  'workers.personality': 'Personality',
  'workers.personality_traits': 'Personality Traits',
  'workers.placeholder_text_area':
    'Define particularly the style and tone your worker would use in a conversation',
  'workers.prompting': 'Prompting',
  'workers.prompting_will_be_automatically_generated_as_the_following_format':
    'Prompting will be automatically generated as the following format',
  'workers.remove_system_tag': 'Remove system tag',
  'workers.removed_system_tag': 'Worker removed system tag',
  'workers.send_lets_start_to_begin': "Send Let's start to begin",
  'workers.style_description_1': 'Style Description 1',
  'workers.style_description_2': 'Style Description 2',
  'workers.style_description_3': 'Style Description 3',
  'workers.successfully_created_worker':
    'Successfully created worker, run it now!',
  'workers.successfully_deleted_worker': 'Successfully deleted worker',
  'workers.successfully_duplicated_worker': 'Successfully duplicated worker',
  'workers.successfully_updated_worker':
    'Successfully updated worker, run it now!',
  'workers.type_in_name_for_your_worker': 'Type in name for your worker',
  'workers.type_in_the_background_of_worker':
    'Type in the background of worker, what his role is and what he could do',
  'workers.type_in_the_personality_traits_of_worker':
    'Describes how your worker tends to think, feel, and behave on an ongoing basis',
  'workers.type_in_the_prompt_for_this_worker':
    'Type in the prompt for this worker',
  'workers.update_worker': 'Update worker',
  'workers.which_help_you_interact_effectively_with_users':
    'which help you interact effectively with users. You are capable of performing and resolving task by using your special abilities and communicating in a',
  'workers.worker_name': 'Worker Name',
  'workers.worker_profile': 'Worker Profile',
  'workers.worker_represents_the_employee_in_your_business_process':
    'The worker represents the employee in your business process. He can perform all assigned tasks using support tools or AI Power.',
  'workers.you_can_effectively_assist_users_who_speak_this_language':
    'you can effectively assist users who speak this language.',
  'workers.you_can_speak_in_other_language_if_user_asked_you_to':
    'You can speak in other language if user asked you to, otherwise, you should answer in ',
  'workers.you_could_customize_this': 'You totally could customize this.',
  'workers.you_exhibit_in_ways_of': 'You exhibit in ways of ',
  'workers.you_re_an_employee_designed_to_assist_users_by_leveraging_your_background':
    "You're an employee designed to assist users by leveraging your background:",
  'workers.your_name_is': 'Your name is ',
  'workflow_breadcrumb.placeholder_description':
    'Type in description for workflow',
  'workflow_breadcrumb.placeholder_name': 'Type in workflow name',
  'workflow.publish_workflow': 'Publish workflow',
  'workflow.publish_workflow.description':
    'Please confirm to grant the purchaser authorization to utilize these private workers',
  'workflow.unknown_workflow': 'Unknown workflow',
  'workflowdetail.a_brief_summary_of_the_email_s_content':
    'A brief summary of the email’s content',
  'workflowdetail.activate_the_workflow_when_a_specific_events_within_platforms_such_as_slack_outlook_happens':
    'Activate the workflow when a specific events within platforms such as Slack, Outlook happens',
  'workflowdetail.add': 'Add',
  'workflowdetail.add_field': 'Add field',
  'workflowdetail.add_file': 'Add file',
  'workflowdetail.add_file_process':
    'Add files and choose AI operation to execute',
  'workflowdetail.add_input_parameters': 'Add input parameters',
  'workflowdetail.add_knowledge_base': 'Add knowledge base',
  'workflowdetail.add_model': 'Add model',
  'workflowdetail.add_new': 'Add new',
  'workflowdetail.add_toolkit': 'Add toolkits',
  'workflowdetail.add_trigger': 'Add trigger',
  'workflowdetail.add_worker': 'Add worker',
  'workflowdetail.advanced': 'Advanced',
  'workflowdetail.advanced_options': 'Advanced options',
  'workflowdetail.ai_enhance': 'AI Enhance',
  'workflowdetail.api_base': 'API Base',
  'workflowdetail.api_configuration': 'API configuration',
  'workflowdetail.api_endpoint': 'API Endpoint',
  'workflowdetail.assign_worker': 'Assign worker',
  'workflowdetail.assign_worker_tooltip':
    'Select worker who has the compatible ability to handle task in this workflow node',
  'workflowdetail.attachments': 'Attachments',
  'workflowdetail.attachments_space': ' Attachments ',
  'workflowdetail.authentication': 'Authentication',
  'workflowdetail.body': 'Body',
  'workflowdetail.body_type': 'Body type',
  'workflowdetail.chat_base': 'Chat Base',
  'workflowdetail.chat_base_integration': 'Chat Base integration',
  'workflowdetail.choose_data_column_from_the_external_knowledge_base_that_will_be_used_as_criteria_for_generating_recommendations':
    'Choose data column from the external knowledge base that will be used as criteria for generating recommendations',
  'workflowdetail.choose_data_column_from_the_external_knowledge_base_that_will_be_used_as_mapping_data_for_generating_recommendations':
    'Choose data column from the external knowledge base that will be used as mapping data for generating recommendations',
  'workflowdetail.choose_data_source_used_for_querying_searching_and_replying':
    'Choose data source used for querying, searching & replying',
  'workflowdetail.choose_knowledge_file_from_your_directories':
    'Choose knowledge file from your directories',
  'workflowdetail.choose_toolkit_to_support_worker_execute_task':
    'Add tools to support worker execute task',
  'workflowdetail.client_id': 'Client ID',
  'workflowdetail.client_secret': 'Client Secret ',
  'workflowdetail.configure_input_params_event_to_trigger_on_to_activate_your_automation_process':
    'Configure input params & event to trigger on to activate your automation process',
  'workflowdetail.configure_meaningful_information_need_to_be_extracted_and_stored_in_long_term_memory':
    'Configure meaningful information need to be extracted and stored in long-term memory',
  'workflowdetail.configure_method_authentication_type_and_needed_elements':
    'Configure method, authentication type and needed elements',
  'workflowdetail.configure_out_params_to_specify_what_will_be_returned':
    'Configure out params to specify what will be returned',
  'workflowdetail.configure_output_format': 'Configure output format',
  'workflowdetail.configure_tool_parameter_to_execute_function':
    'Configure tool parameter to execute function',
  'workflowdetail.connect_external_source': 'Connect external source',
  'workflowdetail.connect_internal_source': 'Connect internal source',
  'workflowdetail.connect_to_your_external_data':
    'Connect to your external data',
  'workflowdetail.connected': 'Connected',
  'workflowdetail.connection': 'Connection',
  'workflowdetail.connection_disabled': 'Connection disabled',
  'workflowdetail.conversation_node': 'Conversation node',
  'workflowdetail.credentials': 'Credentials',
  'workflowdetail.credentials_are_required': 'Credentials are required',
  'workflowdetail.criterion': 'Criterion',
  'workflowdetail.data_column_in_criterion_is_required':
    'Data column in Criterion is required',
  'workflowdetail.data_column_in_recommendation_data_is_required':
    'Data column in Recommendation Data is required',
  'workflowdetail.data_type': 'Data type',
  'workflowdetail.data_types': 'Data type: {{type}}',
  'workflowdetail.default_value': 'Default value',
  'workflowdetail.define_all_parameters_required_for_successful_api_calls_along_with_their_default_values':
    'Define all parameters required for successful API calls along with their default values',
  'workflowdetail.define_how_and_when_workflow_will_begin':
    'Define how and when workflow will begin',
  'workflowdetail.define_worker_responsibility_to_specify_what_is_his_role_and_what_he_need_to_do_to_complete_task_in_this_step':
    'Define worker responsibility to specify what is his role and what he need to do to complete task in this step',
  'workflowdetail.describe_how_your_workflow_gonna_be':
    'Describe how your workflow gonna be...\neg:\nA workflow for a virtual dermatology clinic. The workflow involves several steps, including client check-in, diagnosis and treatment recommendation by a doctor, and payment processing by a cashier. The workflow caters to clients of different age groups, with separate paths for those above and below 18 years old.',
  'workflowdetail.describe_what_is_included_in_the_body_of_the_request':
    'Describe what is included in the body of the request',
  'workflowdetail.describe_when_the_api_request_will_be_sent':
    'Describe when the API request will be sent',
  'workflowdetail.describe_when_the_tool_will_be_executed':
    'Describe when the tool will be executed',
  'workflowdetail.describe_when_the_tool_will_be_used':
    'Describe when the tool will be used',
  'workflowdetail.description': 'Description',
  'workflowdetail.determine_which_worker_will_complete_this_task_and_specify_what_he_needs_to_do':
    'Determine which worker will complete this task and specify what he needs to do',
  'workflowdetail.document_citation': 'Document citation',
  'workflowdetail.duplicated_domain': 'Duplicated domain',
  'workflowdetail.duplicated_field_name': 'Duplicated field name',
  'workflowdetail.duplicated_param_name': 'Duplicated param name',
  'workflowdetail.edge_between_nodes_is_missing':
    'Edge between nodes is missing',
  'workflowdetail.email_of_recipients_use_comma_to_separate':
    'Email of recipients, use comma to separate',
  'workflowdetail.embedded_chatbot': 'Embedded chatbot',
  'workflowdetail.enable_file_transmission_during_api_calls_to_execute_workflows':
    'Enables file transmission during API calls to execute workflows',
  'workflowdetail.enter_fixed_value_for_parameter':
    'Enter fixed value for parameter',
  'workflowdetail.enter_fixed_value_for_variable':
    'Enter fixed value for variable',
  'workflowdetail.enter_parameters_to_run_your_automation_process':
    'Enter parameters to run your automation process',
  'workflowdetail.enter_value': 'Enter value',
  'workflowdetail.environment_variables': 'Environment variables',
  'workflowdetail.excel_column_name': 'Excel column name',
  'workflowdetail.external_source': 'External source',
  'workflowdetail.failed_to_get_authorization_url':
    'Failed to get authorization URL',
  'workflowdetail.false': 'FALSE',
  'workflowdetail.field_name': 'Field name',
  'workflowdetail.file_attachment': 'File attachment',
  'workflowdetail.filter_email_sent_from_one_of_added_sender_s_emails_press_enter_or_comma_to_complete_adding':
    'Filter email sent from one of added sender’s emails, press “Enter” or comma to complete adding',
  'workflowdetail.filter_email_sent_to_folders_by_typing_in_folder_s_display_name_press_enter_or_comma_to_complete_adding':
    'Filter email sent to folders by typing in folder’s display name, press “Enter” or comma to complete adding',
  'workflowdetail.filters': 'Filters',
  'workflowdetail.fixed_value': 'Fixed value',
  'workflowdetail.folders': 'Folders',
  'workflowdetail.follow_json_schema': 'Follow JSON Schema',
  'workflowdetail.form_data': 'Form data',
  'workflowdetail.general': 'General',
  'workflowdetail.headers': 'Headers',
  'workflowdetail.here': 'here',
  'workflowdetail.hi_i_am_your_omnipotent_virtual_assistant':
    'Hi, I am your omnipotent virtual assistant! Tell me what you need and I am here to help',
  'workflowdetail.if_continue_your_customers_will_no_longer_be_able_to_reach_you_via_this_application':
    'If continue, your customers will no longer be able to reach you via this application',
  'workflowdetail.input_parameters': 'Input parameters',
  'workflowdetail.input_parameters_added_one': '{{count}} parameter added',
  'workflowdetail.input_parameters_added_other': '{{count}} parameters added',
  'workflowdetail.integrate': 'Integrate',
  'workflowdetail.integrate_workflow_with_your_conversational_process_which_your_clients_could_directly_interact_with':
    'Integrate workflow with your conversational process which your clients could directly interact with',
  'workflowdetail.integration': 'Integration',
  'workflowdetail.internal_source': 'Internal source',
  'workflowdetail.invalid_email_address': 'Invalid email address',
  'workflowdetail.invalid_json_schema': 'Invalid JSON Schema',
  'workflowdetail.key': 'Key',
  'workflowdetail.leverage_ai_powered_apis_to_enhance_system_efficiency_and_streamline_operations':
    'Leverage AI-powered APIs to enhance system efficiency and streamline operations',
  'workflowdetail.mail_body': 'Mail body',
  'workflowdetail.mail_subject': 'Mail subject',
  'workflowdetail.manage_output_expectations': 'Manage output expectations',
  'workflowdetail.maximum_3_domains': 'Maximum 3 domains',
  'workflowdetail.maximum_5': 'Maximum 5 {{validFilter}}',
  'workflowdetail.memory': 'Memory',
  'workflowdetail.memory_settings': 'Memory settings',
  'workflowdetail.method': 'Method',
  'workflowdetail.missing_mapping_data_in_criterion':
    'Missing mapping data in Criterion',
  'workflowdetail.missing_or_invalid_tool_parameters':
    'Missing or invalid tool parameters',
  'workflowdetail.missing_output_data':
    'Missing output data in Recommendation Data',
  'workflowdetail.missing_output_params_in_end_node':
    'Missing output params in End node',
  'workflowdetail.more_configuration': 'More configuration',
  'workflowdetail.new_inbox_received': 'New inbox received',
  'workflowdetail.no_input_parameters_needed': 'No input parameters needed',
  'workflowdetail.no_parameters_added_to_your_code_function':
    'No parameters added to your code function',
  'workflowdetail.node_configuration': 'Node configuration',
  'workflowdetail.of_successfully_uploaded_files':
    'of successfully uploaded files',
  'workflowdetail.output_format': 'Output format',
  'workflowdetail.output_parameter': 'Output parameter',
  'workflowdetail.param_name': 'Param name',
  'workflowdetail.parameter_name': 'Parameter name',
  'workflowdetail.parameters': 'Parameters',
  'workflowdetail.parameters_are_required': 'Parameters are required',
  'workflowdetail.password': 'Password',
  'workflowdetail.paste_client_id_of_your_application_in_azure_active_directory':
    'Paste Client ID of your application in Azure Active Directory',
  'workflowdetail.paste_client_secret_created_in_your_application_in_azure_active_directory':
    'Paste Client Secret created in your application in Azure Active Directory',
  'workflowdetail.paste_directory_id_of_your_azure_active_directory':
    'Paste Directory ID of your Azure Active Directory',
  'workflowdetail.preview': 'Preview',
  'workflowdetail.processing_node': 'Processing node',
  'workflowdetail.processing_node_configuration':
    'Processing node configuration',
  'workflowdetail.provides_contextually_relevant_questions_to_enhance_user_interaction_and_facilitate_deeper_exploration_of_topics':
    'Provides contextually relevant questions to enhance user interaction and facilitate deeper exploration of topics',
  'workflowdetail.query_parameters': 'Query parameters',
  'workflowdetail.raw': 'Raw',
  'workflowdetail.re_connecting': 'Re-connecting...',
  'workflowdetail.recipients': 'Recipients',
  'workflowdetail.recommendation_data': 'Recommendation Data',
  'workflowdetail.reconnect': 'Reconnect',
  'workflowdetail.refresh_token': 'Refresh Token',
  'workflowdetail.reject_previous_file_processing':
    'Reject previous file processing',
  'workflowdetail.related_questions_suggestion': 'Related questions suggestion',
  'workflowdetail.remove': 'Remove',
  'workflowdetail.remove_integration': 'Remove integration?',
  'workflowdetail.request_configuration': 'Request configuration',
  'workflowdetail.run_your_workflow_here': 'Run your workflow here',
  'workflowdetail.sample': 'Sample',
  'workflowdetail.seamlessly_generates_citations_based_on_information_sourced_from_the_integrated_knowledge_base':
    'Seamlessly generates citations based on information sourced from the integrated knowledge base',
  'workflowdetail.search_external_data': 'Search external data',
  'workflowdetail.section_to_obtain_the': 'section to obtain the ',
  'workflowdetail.select_application_type': 'Select application',
  'workflowdetail.select_assignee': 'Select assignee',
  'workflowdetail.select_connection': 'Select connection',
  'workflowdetail.selected': '{{count}} selected',
  'workflowdetail.senders': 'Senders',
  'workflowdetail.set_up_worker_in_each_workflow_step_node_to_handle_task':
    'Set up worker in each workflow step node to handle task',
  'workflowdetail.set_up_workflow_step_node_to_handle_task':
    'Set up workflow step node to handle task',
  'workflowdetail.sign_in_with_linkedin': 'Sign in with LinkedIn',
  'workflowdetail.sign_in_with_microsoft_outlook':
    'Sign in with Microsoft Outlook',
  'workflowdetail.spaces_special_characters_and_punctuation_are_not_allowed':
    'Spaces, special characters and punctuation are not allowed',
  'workflowdetail.start_node_configuration': 'Start node configuration',
  'workflowdetail.step_description': 'Step description',
  'workflowdetail.step_name': 'Step name',
  'workflowdetail.successfully_created_workflow':
    'Successfully created workflow',
  'workflowdetail.successfully_removed_integration':
    'Successfully removed integration',
  'workflowdetail.successfully_save_memory_settings':
    'Successfully save memory settings',
  'workflowdetail.successfully_updated_integration':
    'Successfully {{status}} integration',
  'workflowdetail.successfully_updated_workflow':
    'Successfully updated workflow',
  'workflowdetail.text_area_placeholder_to_define_worker_responsibility':
    'eg:\nYou are a helpful AI assistant. Solve tasks using your coding and language skills.\nIn the following cases, suggest python code (in a python coding block) or shell script (in a sh coding block) for the user to execute.\nWhen you need to collect info, use the code to output the info you need, for example, browse or search the web, download/read a file, print the content of a web page or a file, get the current date/time, check the operating system',
  'workflowdetail.the_main_content_of_the_email':
    'The main content of the email',
  'workflowdetail.to_attach_files_to_the_automation_process_please_upload_them_using_the_API_in_the':
    'To attach files to the automation process, please upload them using the API in the',
  'workflowdetail.token_endpoint': 'Token Endpoint',
  'workflowdetail.token_has_been_expired': 'Token has been expired!',
  'workflowdetail.tool_parameters': 'Tool parameters',
  'workflowdetail.trigger_condition': 'Trigger condition',
  'workflowdetail.trigger_event': 'Trigger event',
  'workflowdetail.trigger_required': 'Trigger required',
  'workflowdetail.trigger_when': 'Trigger when',
  'workflowdetail.true': 'TRUE',
  'workflowdetail.type_in_column_name': 'Type in column name',
  'workflowdetail.type_in_description_to_specify_what_would_be_completed_in_this_step':
    'Type in description to specify what would be completed in this step',
  'workflowdetail.type_in_here': 'Type in here',
  'workflowdetail.type_in_information_that_your_worker_need_to_remember':
    'Type in information that your worker need to remember',
  'workflowdetail.type_in_mailbox_used_to_catch_trigger_event':
    'Type in mailbox used to catch trigger event',
  'workflowdetail.type_in_url': 'Type in URL',
  'workflowdetail.type_in_workflow_name': 'Type in workflow name',
  'workflowdetail.unnamed_connection': 'Unnamed connection',
  'workflowdetail.upload_file': 'Upload File',
  'workflowdetail.url': 'URL',
  'workflowdetail.value': 'Value',
  'workflowdetail.variable_name': 'Variable name',
  'workflowdetail.variable_type': 'Variable type',
  'workflowdetail.worker': 'Worker',
  'workflowdetail.worker_responsibility': 'Worker responsibility',
  'workflowdetail.workflow_integration': 'Workflow Integration',
  'workflowdetail.workflow_memory': 'Workflow Memory',
  'workflowdetail.workflow_node_is_required': 'Workflow node is required',
  'workflowdetail.workflow_step_is_invalid': 'Workflow step is invalid',
  'workflowintegration.access_token': 'Access Token',
  'workflowintegration.access_token_zalo': 'Zalo OA Access Token',
  'workflowintegration.ai_agency_callback_api': 'AI Agency Callback API',
  'workflowintegration.app_id': 'App ID',
  'workflowintegration.app_secret': 'Bot App secret',
  'workflowintegration.app_secret_ws': 'App Secret',
  'workflowintegration.application_information': 'Application information',
  'workflowintegration.authentication': 'Authentication',
  'workflowintegration.complete_the_integration_process_to_preview':
    'Complete the integration process to preview',
  'workflowintegration.configuration': 'Configuration',
  'workflowintegration.copy_and_paste_access_token_after_configure_whatsapp_to_your_application':
    'Copy and paste generated access token after configure WhatsApp to your application',
  'workflowintegration.copy_and_paste_access_token_here':
    'Copy and paste token here',
  'workflowintegration.copy_and_paste_access_token_zalo_here':
    'Copy and paste generated access token here',
  'workflowintegration.copy_and_paste_app_id_here':
    'Copy and paste application id here',
  'workflowintegration.copy_and_paste_app_id_in_app_settings':
    'Copy and paste app id in App Settings',
  'workflowintegration.copy_and_paste_app_secret_in_app_settings':
    'Copy and paste app secret in App Settings',
  'workflowintegration.copy_and_paste_bot_app_secret_here':
    'Copy and paste bot app secret here',
  'workflowintegration.copy_and_paste_bot_page_id_here':
    'Copy and paste bot page id here',
  'workflowintegration.copy_and_paste_generated_access_token_here':
    'Copy and paste generated access token here',
  'workflowintegration.copy_and_paste_official_domain_here':
    'Press “Enter” to complete adding, maximum 3 domains',
  'workflowintegration.copy_and_paste_phone_number_id_in_app_settings':
    'Copy and paste phone number id in application page',
  'workflowintegration.copy_and_paste_refresh_token_here':
    'Copy and paste generated refresh token here',
  'workflowintegration.copy_and_paste_sandbox_domain_here':
    'Press “Enter” to complete adding your test environment, maximum 3 domains',
  'workflowintegration.copy_and_paste_secret_key_here':
    'Copy and paste generated secret key here',
  'workflowintegration.copy_and_paste_whatsapp_account_id_in_app_settings':
    'Copy and paste WhatsApp account id in Application page',
  'workflowintegration.disable': 'Disable',
  'workflowintegration.embed_chatbot': 'Embed chatbot to your website',
  'workflowintegration.embedded_iframe': 'Embedded iframe',
  'workflowintegration.enable': 'Enable',
  'workflowintegration.export_and_integrate_workflow_with_your_business':
    'Export and integrate workflow with your business',
  'workflowintegration.genai_callback_api': 'GenAI Callback API',
  'workflowintegration.integrate': 'Integrate',
  'workflowintegration.messenger_bot_token': 'Messenger Bot Token',
  'workflowintegration.messenger_integration': 'Integrate to Messenger',
  'workflowintegration.official_domain': 'Official Domain',
  'workflowintegration.page_id': 'Bot Page Id',
  'workflowintegration.phone_number_id': 'Phone Number ID',
  'workflowintegration.preview': 'Preview',
  'workflowintegration.remove': 'Remove',
  'workflowintegration.sandbox_domain': 'SandBox Domain',
  'workflowintegration.secret_key': 'Secret key',
  'workflowintegration.send_newbot_to_botfather_on_telegram_to_get_the_token':
    'Send /newbot to BotFather on Telegram to get the token',
  'workflowintegration.successfully_created_integration':
    'Successfully created integration',
  'workflowintegration.successfully_created_Telegram_integration':
    'Successfully integrated workflow to Telegram',
  'workflowintegration.successfully_created_Whatsapp_integration':
    'Successfully integrated workflow to Whatsapp',
  'workflowintegration.successfully_created_Zalo_integration':
    'Successfully integrated workflow to Zalo',
  'workflowintegration.successfully_updated_integration':
    'Successfully updated integration',
  'workflowintegration.successfully_updated_Telegram_integration':
    'Successfully updated Telegram integration',
  'workflowintegration.successfully_updated_Whatsapp_integration':
    'Successfully updated Whatsapp integration',
  'workflowintegration.successfully_updated_Zalo_integration':
    'Successfully updated Zalo integration',
  'workflowintegration.telegram_bot_token': 'Telegram Bot Token',
  'workflowintegration.telegram_integration': 'Integrate to Telegram',
  'workflowintegration.token': 'Token',
  'workflowintegration.url': 'URL',
  'workflowintegration.verify_token': 'Verify Token',
  'workflowintegration.whatsapp_business_account_id': 'WhatsApp Account ID',
  'workflowintegration.whatsapp_integration': 'Integrate to Whatsapp',
  'workflowintegration.workflow_integration': 'Workflow Integration',
  'workflowintegration.zalo_app_id': 'Zalo App ID',
  'workflowintegration.zalo_app_secret': 'Zalo App Secret',
  'workflowintegration.zalo_integration': 'Integrate to Zalo',
  'workflows.add_category': 'Add category',
  'workflows.add_new_workflow': 'Add new workflow',
  'workflows.add_to_extension': 'Add to extension',
  'workflows.added': 'added',
  'workflows.all_categories': 'Category',
  'workflows.automated': 'Automated',
  'workflows.automated_workflow': 'Automated workflow',
  'workflows.business_category': 'Business Category',
  'workflows.cancel_publishing': 'Cancel publishing',
  'workflows.cancel_publishing_workflow': 'Cancel publishing workflow?',
  'workflows.choose_the_appropriate_category_for_your_workflow':
    'Choose the business category for your workflow',
  'workflows.click_here_to_create_your_own_workflow':
    'Click here to create your own workflow',
  'workflows.clone': 'Clone',
  'workflows.community': 'Community',
  'workflows.configure_your_own_workflow_using_ai_powered_worker_to_enhance_business_operations_and_user_experiences':
    'Configure your own workflow using AI-Powered worker to enhance business\noperations and user experiences',
  'workflows.conversational': 'Conversational',
  'workflows.conversational_workflow': 'Conversational workflow',
  'workflows.delete': 'Delete',
  'workflows.delete_workflow': 'Delete workflow?',
  'workflows.describe_the_workflow_you_want_to_create':
    'Describe the workflow that you want to create',
  'workflows.duplicate': 'Duplicate',
  'workflows.finance': 'Finance',
  'workflows.generate': 'Generate',
  'workflows.grant_global_access': 'Grant global access',
  'workflows.human_resources': 'Human Resources',
  'workflows.if_continue_this_workflow_will_be_permanently_removed':
    'If continue, this workflow will be permanently removed. This action cannot be reverted!',
  'workflows.if_continue_this_workflow_will_be_unlisted_and_hide_from_the_marketplace':
    'If continue, this workflow will be unlisted and hide from the marketplace.',
  'workflows.marketing': 'Marketing',
  'workflows.new_workflow': 'New workflow',
  'workflows.process_that_involves_dynamic_interaction_between_AI_and_humans_to_complete_tasks':
    'Process that involves dynamic interaction between AI and humans to complete tasks',
  'workflows.process_that_relies_entirely_on_AI_to_handle_tasks_from_start_to_finish_with_minimal_human_involvement':
    'Process that relies entirely on AI to handle tasks from start to finish with minimal human involvement',
  'workflows.publish_workflow': 'Publish to marketplace',
  'workflows.remove_from_extension': 'Remove from extension',
  'workflows.research_and_development': 'Research & Development',
  'workflows.revoke_global_access': 'Revoke global access',
  'workflows.sales': 'Sales',
  'workflows.start_from_scratch': 'Start from scratch',
  'workflows.successfully_added_to_extension':
    'Successfully added workflow to extension',
  'workflows.successfully_cancelled_publishing':
    'Successfully cancelled publishing',
  'workflows.successfully_cloned_workflow':
    'Successfully cloned workflow template',
  'workflows.successfully_deleted_workflow': 'Successfully deleted workflow',
  'workflows.successfully_duplicated_workflow':
    'Successfully duplicated workflow',
  'workflows.successfully_removed_from_extension':
    'Successfully remove workflow from extension',
  'workflows.total': 'Total: {{total}}',
  'workflows.type': 'Type',
  'workflows.uncategorized': 'Uncategorized',
  'workflows.update_category': 'Update category',
  'workflows.updated': 'updated',
  'workflows.workflow_category': 'Workflow {{action}} category',
  'workflows.workflow_category_title': 'Workflow category',
  'workflowstemplate.business_category': 'Business Category',
  'workflowstemplate.category_sample_3': 'Category Sample 3',
  'workflowstemplate.clone_workflow_template': 'Clone workflow template',
  'workflowstemplate.confirm_to_buy_all_tools_and_workers':
    'Please confirm to buy all tools and workers to proceed',
  'workflowstemplate.discover_purchase_and_share_customizable_workflow_templates_designed_to_streamline_business_processes':
    'Discover, purchase, and share customizable workflow templates designed to streamline business processes',
  'workflowstemplate.get_all_free': 'Get all free',
  'workflowstemplate.integration': 'Integration',
  'workflowstemplate.knowledge_management': 'Knowledge Management',
  'workflowstemplate.productivity': 'Productivity',
  'workflowstemplate.sales': 'Sales',
  'workflowstemplate.social_media': 'Social Media',
  'workflowstemplate.successfully_bought_tool': 'Successfully bought tool',
  'workflowstemplate.support': 'Support',
  'workflowstemplate.team_collaboration': 'Team Collaboration',
  'workflowstemplate.tools_one': 'Tool: {{count}}',
  'workflowstemplate.tools_other': 'Tools: {{count}}',
  'workflowstemplate.total': 'Total',
  'workflowstemplate.web_development': 'Web Development',
  'workflowstemplate.workers_one': 'Worker: {{count}}',
  'workflowstemplate.workers_other': 'Workers: {{count}}',
  'workflowstemplate.workflow_market': 'Workflow Market',
  'tool_collection.successfully_created_tool': 'Successfully created tool',
  'chatbotplans.chatbot_plans': 'Chatbot Plans',
  'chatbotplans.save_9_on_yearly_subscription_plans':
    '<gradient>Save 9%</gradient> on yearly subscription plans',
  'chatbotplans.trial': 'Trial',
  'chatbotplans.trial_description': 'Best for individual use, trial 1 month',
  'chatbotplans.professional': 'Professional',
  'chatbotplans.professional_description': 'Best for innovative team',
  'chatbotplans.business': 'Business',
  'chatbotplans.business_description': 'Best for growing business',
  'chatbotplans.enterprise': 'Enterprise',
  'chatbotplans.enterprise_description':
    'Best performance, support and security',
  'chatbotplans.tokens_per_month':
    '<bold>{{token_quantity}}</bold> tokens per month',
  'chatbotplans.knowledge_base':
    '<bold>{{storage_quantity}}MB</bold> of Knowledge Base',
  'chatbotplans.integration_channel':
    '<bold>{{channels}}</bold> integration channel',
  'chatbotplans.customer_service_support_within_48h':
    'Customer service support within <bold>48h</bold>',
  'chatbotplans.complete_implementation_in_5_days':
    'Complete implementation in <bold>5 days</bold>',
  'chatbotplans.integration_channels':
    '<bold>{{channels}}</bold> integration channels',
  'chatbotplans.collect_customer_info_to_gg_sheet':
    'Collect customer info to GG Sheet',
  'chatbotplans.customer_service_support_within_24h':
    'Customer service support within <bold>24h</bold>',
  'chatbotplans.evaluate_customer_potential': 'Evaluate customer potential',
  'chatbotplans.unlimited_tokens_per_month':
    '<bold>Unlimited</bold> tokens per month',
  'chatbotplans.negotiated_knowledge_base_capacity':
    'Negotiated Knowledge Base capacity',
  'chatbotplans.multi_integration_channels':
    '<bold>Mullti</bold> integration channels',
  'chatbotplans.advanced_user_profile_collection':
    'Advanced user profile collection',
  'chatbotplans.customized_customer_evaluation':
    'Customized customer evaluation',
  'chatbotplans.dedicated_customer_support':
    '<bold>Dedicated</bold> customer support',
  'chatbotplans.negotiated_implementation_time':
    'Negotiated implementation time',
  'common.contact': 'Contact',
  'common.month': 'month',
  'common.year': 'year',
  'common.required': 'Required!',
  'contact.type_in_your_name': 'Type in your name here',
  'contact.type_in_your_phone_number': 'Type in your phone number here',
  'contact.type_in_your_email': 'Type in your email here',
  'contact.tell_us_your_expectation':
    'Tell us your expectations or anything you need to customize your plan',
  'contact.contact_sales': 'Contact Sales',
  'contact.your_name': 'Your name',
  'contact.phone_number': 'Phone number',
  'contact.expectation': 'Expectation',
  'contact.task_to_our_sales': 'Task to our Sales',
  'contact.get_your_best_deal': 'Get your best deal!',
  'contact.receive_a_pricing_structure':
    'Receive a pricing structure designed to maximize value based on your unique needs.',
  'contact.get_a_plan_tailored_to_your_specific_needs':
    ' Get a plan tailored to your specific needs, ensuring the features and resources align perfectly with your goals.',
  'contact.enjoy_flexibility_to_adjust_the_plan':
    'Enjoy flexibility to adjust the plan as your requirements grow, providing cost-effective scalability.',
  'contact.access_dedicated_premium_support':
    'Access dedicated, premium support from our sales team to ensure a seamless experience.',
  'contact.fill_in_your_contact_details':
    'Fill in your contact details and we’ll get back to you shortly.',
}
