import { CurrencyTypes, SubscriptionUpgradePublic } from '@/apis/client'

export interface ISubscription {
  id: string
  name: string
  price: number | undefined
  curentcy: CurrencyTypes
  token_quantity: number | undefined
  channels: number | undefined
  storage_quantity: number | undefined
}

export const transformSubscriptions = (
  subscriptions: SubscriptionUpgradePublic[]
) => {
  const result: {
    month: Array<ISubscription>
    year: Array<ISubscription>
  } = {
    month: [],
    year: [],
  }

  subscriptions.forEach((sub) => {
    const item: ISubscription = {
      id: sub.id,
      name: sub.package_name,
      price: sub.price,
      curentcy: sub.currency,
      token_quantity: sub.token_quantity,
      channels: sub.channels,
      storage_quantity: sub.storage_quantity,
    }

    if (sub.duration === 1) {
      result.month.push(item)
    } else if (sub.duration === 12) {
      result.year.push(item)
    } else if (sub.duration === 0) {
      result.month.push(item)
      result.year.push(item)
    }
  })

  return result
}

export enum ESubscriptionType {
  Trial1Month = '00000000-0000-0000-0000-000000000003',
  Professional1Month = '00000000-0000-0000-0000-000000000004',
  Bussiness1Month = '00000000-0000-0000-0000-000000000006',
  Professional1Year = '00000000-0000-0000-0000-000000000005',
  Bussiness1Year = '00000000-0000-0000-0000-000000000007',
  Enterprice = '00000000-0000-0000-0000-000000000008',
}
