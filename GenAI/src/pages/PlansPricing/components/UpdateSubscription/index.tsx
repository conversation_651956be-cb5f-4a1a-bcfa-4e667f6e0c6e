import BaseModal from '@/components/BaseModal'
import Text from '@/components/Text'
import { Trans, useTranslation } from 'react-i18next'
import ItemPlan from '../ItemPlan'
import { Segmented } from 'antd'
import { subscriptionReadSubscriptionsApi } from '@/apis/client'
import { HTTP_STATUS_CODE } from '@/constants'
import { useEffect, useState } from 'react'
import Message from '@/components/Message'
import { ISubscription, transformSubscriptions } from '../../const'
import './style.scss'
import clsx from 'clsx'
import Icon from '@/assets/icon/Icon'
import Input from '@/components/Input'
import Button from '@/components/Button'
import TextArea from '@/components/TextArea'
import { colors } from '@/theme'
import IconButton from '@/components/IconButton'

interface UpdateSubscriptionProps {
  isOpen: boolean
  onClose: () => void
}

const UpdateSubscription = ({ isOpen, onClose }: UpdateSubscriptionProps) => {
  const { t } = useTranslation()

  const [selectedPlan, setSelectedPlan] = useState<string>('Monthly')
  const [plans, setPlans] = useState<{
    month: Array<ISubscription>
    year: Array<ISubscription>
  }>({
    month: [],
    year: [],
  })

  const [isContact, setIsContact] = useState<boolean>(false)

  const fetchPlans = async () => {
    try {
      const res = await subscriptionReadSubscriptionsApi({
        query: {
          page_number: 1,
          page_size: 100,
          package_type: 'Solution',
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        if (res.data?.data?.data)
          setPlans(transformSubscriptions(res.data?.data?.data))
      }
    } catch (error) {
      Message.error({
        message: t('tool_category.something_wrong_please_retry'),
      })
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchPlans()
    }
  }, [isOpen])

  const handleContact = (name: string) => {
    setIsContact(true)
  }

  const handleBack = () => {
    if (isContact) {
      setIsContact(false)
      return
    } else {
      onClose()
    }
  }

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <IconButton
        className="absolute right-[12px] top-[12px] cursor-pointer duration-300 hover:bg-neutral-200"
        nameIcon="x-close"
        sizeIcon={16}
        colorIcon={colors.neutral[500]}
        onClick={handleBack}
      />
      {isContact ? (
        <div className="flex w-[797px] justify-center gap-[20px] rounded-[20px] bg-white p-[32px] shadow-md">
          <div className="flex w-[315px] min-w-[315px] flex-col gap-[24px]">
            <div className="flex flex-col">
              <Text
                type="heading"
                variant="medium"
                className="text-Primary-Color"
              >
                Task to our Sales
              </Text>
              <Text
                type="body"
                variant="regular"
                className="text-Secondary-Color"
              >
                Get your best deal!
              </Text>
            </div>

            <div className="flex flex-col gap-[20px]">
              <div className="flex gap-[12px]">
                <div className="flex h-fit w-fit items-center justify-center rounded-full bg-[#E9F5ED] p-[4px]">
                  <Icon name="check" color="#199647" size={16} />
                </div>

                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                >
                  Receive a pricing structure designed to maximize value based
                  on your unique needs.
                </Text>
              </div>

              <div className="flex gap-[12px]">
                <div className="flex h-fit w-fit items-center justify-center rounded-full bg-[#E9F5ED] p-[4px]">
                  <Icon name="check" color="#199647" size={16} />
                </div>

                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                >
                  Get a plan tailored to your specific needs, ensuring the
                  features and resources align perfectly with your goals.
                </Text>
              </div>

              <div className="flex gap-[12px]">
                <div className="flex h-fit w-fit items-center justify-center rounded-full bg-[#E9F5ED] p-[4px]">
                  <Icon name="check" color="#199647" size={16} />
                </div>

                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                >
                  Enjoy flexibility to adjust the plan as your requirements
                  grow, providing cost-effective scalability.
                </Text>
              </div>

              <div className="flex gap-[12px]">
                <div className="flex h-fit w-fit items-center justify-center rounded-full bg-[#E9F5ED] p-[4px]">
                  <Icon name="check" color="#199647" size={16} />
                </div>

                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                >
                  Access dedicated, premium support from our sales team to
                  ensure a seamless experience.
                </Text>
              </div>
            </div>
          </div>

          <div className="flex h-full w-full flex-col gap-[32px] rounded-[20px] border border-neutral-400 bg-white px-[44px] pb-[32px] pt-[20px]">
            <Text
              type="body"
              variant="medium"
              className="text-center text-black"
            >
              Fill in your contact details and we’ll get back to you shortly
            </Text>

            <div className="flex w-full flex-col items-center gap-[32px]">
              <div className="flex w-full flex-col gap-[12px]">
                <Input
                  label={t('register.your_name')}
                  placeholder={t('contact.type_in_your_name')}
                  isFullWidth
                  required
                />
                <Input
                  label={t('register.email')}
                  placeholder={'example@company'}
                  isFullWidth
                  required
                />
                <Input
                  label={t('contact.phone_number')}
                  placeholder={t('contact.type_in_your_phone_number')}
                  isFullWidth
                  required
                />
                <TextArea
                  label={t('contact.expectation')}
                  placeholder={t('contact.tell_us_your_expectation')}
                  onChange={() => {}}
                />
              </div>

              <Button
                type="primary"
                className="w-full"
                onClick={() => {}}
                text={t('contact.contact_sales')}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="flex w-[1306px] flex-col items-center justify-center gap-6 rounded-[20px] bg-white px-[40px] pb-[32px] shadow-md">
          <div className="flex w-full flex-col items-center gap-[24px]">
            <div className="flex flex-col items-center gap-[20px] pt-[20px]">
              <Text type="title" variant="semibold" className="text-gradient">
                {t('chatbotplans.chatbot_plans')}
              </Text>

              <div className="flex flex-col items-center gap-[8px]">
                <Segmented
                  className="switch-plan-pricing"
                  size="large"
                  options={['Monthly', 'Yearly']}
                  value={selectedPlan}
                  onChange={(value) => setSelectedPlan(value)}
                />
                <Text type="subBody" className="text-Tertiary-Color">
                  <Trans
                    i18nKey={'chatbotplans.save_9_on_yearly_subscription_plans'}
                    components={{
                      gradient: (
                        <Text
                          type="subBody"
                          variant="regular"
                          className="text-gradient"
                        ></Text>
                      ),
                    }}
                  />
                </Text>
              </div>
            </div>

            <div
              className={clsx(
                'flex w-full items-center justify-center',
                selectedPlan === 'Monthly' && 'gap-[24px]',
                selectedPlan === 'Yearly' && 'justify-between px-[119px]'
              )}
            >
              {selectedPlan === 'Monthly'
                ? plans.month.map((plan) => (
                    <ItemPlan
                      key={plan.id}
                      plan={plan}
                      type={'Monthly'}
                      handleContact={handleContact}
                    />
                  ))
                : plans.year.map((plan) => (
                    <ItemPlan
                      key={plan.id}
                      plan={plan}
                      type={'Yearly'}
                      handleContact={handleContact}
                    />
                  ))}
            </div>
          </div>
        </div>
      )}
    </BaseModal>
  )
}

export default UpdateSubscription
