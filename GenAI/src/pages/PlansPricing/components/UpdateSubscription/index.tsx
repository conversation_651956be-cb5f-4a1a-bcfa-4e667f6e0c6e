import BaseModal from '@/components/BaseModal'
import Text from '@/components/Text'
import { Trans, useTranslation } from 'react-i18next'
import ItemPlan from '../ItemPlan'
import { Segmented } from 'antd'
import {
  subscriptionContactSalesApi,
  subscriptionReadSubscriptionsApi,
} from '@/apis/client'
import { HTTP_STATUS_CODE } from '@/constants'
import { useEffect, useState } from 'react'
import Message from '@/components/Message'
import { ISubscription, transformSubscriptions } from '../../const'
import './style.scss'
import clsx from 'clsx'
import Icon from '@/assets/icon/Icon'
import Input from '@/components/Input'
import Button from '@/components/Button'
import TextArea from '@/components/TextArea'
import { colors } from '@/theme'
import IconButton from '@/components/IconButton'

interface UpdateSubscriptionProps {
  isOpen: boolean
  onClose: () => void
}

const UpdateSubscription = ({ isOpen, onClose }: UpdateSubscriptionProps) => {
  const { t } = useTranslation()

  const [selectedPlan, setSelectedPlan] = useState<string>('Monthly')
  const [plans, setPlans] = useState<{
    month: Array<ISubscription>
    year: Array<ISubscription>
  }>({
    month: [],
    year: [],
  })

  const [isContact, setIsContact] = useState<boolean>(false)
  const [contactForm, setContactForm] = useState<{
    name: string
    email: string
    phone: string
    expectations: string
    subscription_name: string
  }>({
    name: '',
    email: '',
    phone: '',
    expectations: '',
    subscription_name: '',
  })
  const [emailError, setEmailError] = useState<string>('')
  const [nameError, setNameError] = useState<string>('')
  const [isSending, setIsSending] = useState<boolean>(false)

  // Email validation function
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    return emailRegex.test(email)
  }

  // Handle email validation
  const handleEmailValidation = () => {
    const trimmedEmail = contactForm.email.trim()

    // Update email with trimmed value or empty if all whitespace
    setContactForm({
      ...contactForm,
      email: trimmedEmail,
    })

    // Validate email format if not empty
    if (trimmedEmail && !validateEmail(trimmedEmail)) {
      setEmailError('Invalid email address')
    } else {
      setEmailError('')
    }
  }

  // Validate form before submission
  const validateForm = (): boolean => {
    let isValid = true

    // Validate name
    const trimmedName = contactForm.name.trim()
    if (!trimmedName) {
      setNameError('Required!')
      isValid = false
    } else {
      setNameError('')
    }

    // Validate email
    const trimmedEmail = contactForm.email.trim()
    if (!trimmedEmail) {
      setEmailError('Required!')
      isValid = false
    } else if (!validateEmail(trimmedEmail)) {
      setEmailError('Invalid email address')
      isValid = false
    } else {
      setEmailError('')
    }

    // Update form with trimmed values
    setContactForm({
      ...contactForm,
      name: trimmedName,
      email: trimmedEmail,
    })

    return isValid
  }

  const fetchPlans = async () => {
    try {
      const res = await subscriptionReadSubscriptionsApi({
        query: {
          page_number: 1,
          page_size: 100,
          package_type: 'Solution',
        },
      })

      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        if (res.data?.data?.data)
          setPlans(transformSubscriptions(res.data?.data?.data))
      }
    } catch (error) {
      Message.error({
        message: t('tool_category.something_wrong_please_retry'),
      })
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchPlans()
    }
  }, [isOpen])

  const handleContact = (name: string) => {
    setContactForm({
      ...contactForm,
      subscription_name: name,
    })
    setIsContact(true)
  }

  const handleBack = () => {
    if (isContact) {
      setContactForm({
        name: '',
        email: '',
        phone: '',
        expectations: '',
        subscription_name: '',
      })
      setEmailError('')
      setNameError('')
      setIsContact(false)
      return
    } else {
      onClose()
    }
  }

  const handleSendContact = async () => {
    // Validate form before submission
    if (!validateForm()) {
      return
    }

    try {
      setIsSending(true)
      const res = await subscriptionContactSalesApi({
        body: contactForm,
      })
      if (res.status === HTTP_STATUS_CODE.SUCCESS) {
        setContactForm({
          name: '',
          email: '',
          phone: '',
          expectations: '',
          subscription_name: '',
        })
        setEmailError('')
        setNameError('')
        onClose()
      } else {
        Message.error({
          message: t('tool_category.something_wrong_please_retry'),
        })
      }
    } catch (error) {
      Message.error({
        message: t('tool_category.something_wrong_please_retry'),
      })
    }
    setIsSending(false)
  }

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isPureModal>
      <IconButton
        className="absolute right-[12px] top-[12px] cursor-pointer duration-300 hover:bg-neutral-200"
        nameIcon="x-close"
        sizeIcon={16}
        colorIcon={colors.neutral[500]}
        onClick={handleBack}
      />
      {isContact ? (
        <div className="flex w-[797px] justify-center gap-[20px] rounded-[20px] bg-white p-[32px] shadow-md">
          <div className="flex w-[315px] min-w-[315px] flex-col gap-[24px]">
            <div className="flex flex-col">
              <Text
                type="heading"
                variant="medium"
                className="text-Primary-Color"
              >
                {t('contact.task_to_our_sales')}
              </Text>
              <Text
                type="body"
                variant="regular"
                className="text-Secondary-Color"
              >
                {t('contact.get_your_best_deal')}
              </Text>
            </div>

            <div className="flex flex-col gap-[20px]">
              <div className="flex gap-[12px]">
                <div className="flex h-fit w-fit items-center justify-center rounded-full bg-[#E9F5ED] p-[4px]">
                  <Icon name="check" color="#199647" size={16} />
                </div>

                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                >
                  {t('contact.receive_a_pricing_structure')}
                </Text>
              </div>

              <div className="flex gap-[12px]">
                <div className="flex h-fit w-fit items-center justify-center rounded-full bg-[#E9F5ED] p-[4px]">
                  <Icon name="check" color="#199647" size={16} />
                </div>

                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                >
                  {t('contact.get_a_plan_tailored_to_your_specific_needs')}
                </Text>
              </div>

              <div className="flex gap-[12px]">
                <div className="flex h-fit w-fit items-center justify-center rounded-full bg-[#E9F5ED] p-[4px]">
                  <Icon name="check" color="#199647" size={16} />
                </div>

                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                >
                  {t('contact.enjoy_flexibility_to_adjust_the_plan')}
                </Text>
              </div>

              <div className="flex gap-[12px]">
                <div className="flex h-fit w-fit items-center justify-center rounded-full bg-[#E9F5ED] p-[4px]">
                  <Icon name="check" color="#199647" size={16} />
                </div>

                <Text
                  type="subBody"
                  variant="regular"
                  className="text-Secondary-Color"
                >
                  {t('contact.access_dedicated_premium_support')}
                </Text>
              </div>
            </div>
          </div>

          <div className="flex h-full w-full flex-col gap-[32px] rounded-[20px] border border-neutral-400 bg-white px-[44px] pb-[32px] pt-[20px]">
            <Text
              type="body"
              variant="medium"
              className="text-center text-black"
            >
              {t('contact.fill_in_your_contact_details')}
            </Text>

            <div className="flex w-full flex-col items-center gap-[32px]">
              <div className="flex w-full flex-col gap-[12px]">
                <Input
                  label={t('register.your_name')}
                  placeholder={t('contact.type_in_your_name')}
                  value={contactForm.name}
                  maxLength={50}
                  onChange={(e) => {
                    setContactForm({
                      ...contactForm,
                      name: e.target.value,
                    })
                    // Clear error when user starts typing
                    if (nameError) {
                      setNameError('')
                    }
                  }}
                  onBlur={() => {
                    setContactForm({
                      ...contactForm,
                      name: contactForm.name.trim(),
                    })
                  }}
                  isFullWidth
                  isError={!!nameError}
                  errorText={nameError}
                />
                <Input
                  label={t('register.email')}
                  placeholder={'example@company'}
                  type="email"
                  maxLength={255}
                  value={contactForm.email}
                  onChange={(e) => {
                    setContactForm({
                      ...contactForm,
                      email: e.target.value,
                    })
                    // Clear error when user starts typing
                    if (emailError) {
                      setEmailError('')
                    }
                  }}
                  onBlur={handleEmailValidation}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleEmailValidation()
                    }
                  }}
                  isFullWidth
                  isError={!!emailError}
                  errorText={emailError}
                />
                <Input
                  label={t('contact.phone_number')}
                  placeholder={t('contact.type_in_your_phone_number')}
                  value={contactForm.phone}
                  maxLength={50}
                  onBlur={() => {
                    setContactForm({
                      ...contactForm,
                      phone: contactForm.phone.trim(),
                    })
                  }}
                  onChange={(e) => {
                    setContactForm({
                      ...contactForm,
                      phone: e.target.value,
                    })
                  }}
                  isFullWidth
                />
                <TextArea
                  label={t('contact.expectation')}
                  placeholder={t('contact.tell_us_your_expectation')}
                  value={contactForm.expectations}
                  maxLength={255}
                  onChange={(e) => {
                    setContactForm({
                      ...contactForm,
                      expectations: e,
                    })
                  }}
                  onBlur={() => {
                    setContactForm({
                      ...contactForm,
                      expectations: contactForm.expectations.trim(),
                    })
                  }}
                />
              </div>

              <Button
                type="primary"
                className="w-full"
                onClick={handleSendContact}
                text={t('contact.contact_sales')}
                loading={isSending}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="flex w-[1306px] flex-col items-center justify-center gap-6 rounded-[20px] bg-white px-[40px] pb-[32px] shadow-md">
          <div className="flex w-full flex-col items-center gap-[24px]">
            <div className="flex flex-col items-center gap-[20px] pt-[20px]">
              <Text type="title" variant="semibold" className="text-gradient">
                {t('chatbotplans.chatbot_plans')}
              </Text>

              <div className="flex flex-col items-center gap-[8px]">
                <Segmented
                  className="switch-plan-pricing"
                  size="large"
                  options={['Monthly', 'Yearly']}
                  value={selectedPlan}
                  onChange={(value) => setSelectedPlan(value)}
                />
                <Text type="subBody" className="text-Tertiary-Color">
                  <Trans
                    i18nKey={'chatbotplans.save_9_on_yearly_subscription_plans'}
                    components={{
                      gradient: (
                        <Text
                          type="subBody"
                          variant="regular"
                          className="text-gradient"
                        ></Text>
                      ),
                    }}
                  />
                </Text>
              </div>
            </div>

            <div
              className={clsx(
                'flex w-full items-center justify-center',
                selectedPlan === 'Monthly' && 'gap-[24px]',
                selectedPlan === 'Yearly' && 'justify-between px-[119px]'
              )}
            >
              {selectedPlan === 'Monthly'
                ? plans.month.map((plan) => (
                    <ItemPlan
                      key={plan.id}
                      plan={plan}
                      type={'Monthly'}
                      handleContact={handleContact}
                    />
                  ))
                : plans.year.map((plan) => (
                    <ItemPlan
                      key={plan.id}
                      plan={plan}
                      type={'Yearly'}
                      handleContact={handleContact}
                    />
                  ))}
            </div>
          </div>
        </div>
      )}
    </BaseModal>
  )
}

export default UpdateSubscription
