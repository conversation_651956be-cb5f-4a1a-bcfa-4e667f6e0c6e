.switch-plan-pricing {
  padding: 4px !important;
  border-radius: 9999px !important;
  background: #f0f0f0;
  .ant-segmented.ant-segmented-lg .ant-segmented-item-label {
    line-height: 26px !important;
  }

  .ant-segmented-item {
    border-radius: 9999px !important;
  }
  .ant-segmented-group {
    gap: 4px !important;
  }
  .ant-segmented-item-label {
    align-items: center;
    display: flex;
    color: var(--Tertiary-Color, #766d72);

    /* Subbody/Regular */
    font-family: 'Be Vietnam Pro';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; /* 150% */
  }

  .ant-segmented-item-selected {
    .ant-segmented-item-label {
      align-items: center;
      display: flex;
      color: var(--Primary-Color, #2d0136);

      /* Subbody/Regular */
      font-family: 'Be Vietnam Pro';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px; /* 150% */
    }
  }
}
