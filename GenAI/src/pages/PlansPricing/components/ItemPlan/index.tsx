import Text from '@/components/Text'
import IconTrial from '../../assets/IconTrial'
import Button from '@/components/Button'
import Icon from '@/assets/icon/Icon'
import { useEffect, useState } from 'react'
import { ESubscriptionType, ISubscription } from '../../const'
import { Trans, useTranslation } from 'react-i18next'
import IconProfessional from '../../assets/IconProfessional'
import IconBusiness from '../../assets/IconBusiness'
import IconEnterprise from '../../assets/IconEnterprise'

const ItemPlan = ({
  plan,
  type,
  handleContact,
}: {
  plan: ISubscription
  type: 'Monthly' | 'Yearly'
  handleContact: (name: string) => void
}) => {
  const { t } = useTranslation()
  const [content, setContent] = useState<{
    title: string
    description: string
    price: string
    option: string[]
  }>({
    title: '',
    description: '',
    price: '',
    option: [],
  })

  const currentPrice = (curentcy: string) => {
    switch (curentcy) {
      case 'USD':
        return '$'
      case 'VND':
        return 'VND'

      default:
        break
    }
  }

  const getContent = (key: string) => {
    switch (key) {
      case ESubscriptionType.Trial1Month:
        setContent({
          title: t(plan.name),
          description: t('chatbotplans.trial_description'),
          price: `${plan.price}${currentPrice(plan.curentcy)}`,
          option: [
            'chatbotplans.tokens_per_month',
            'chatbotplans.knowledge_base',
            'chatbotplans.integration_channel',
            'chatbotplans.customer_service_support_within_48h',
            'chatbotplans.complete_implementation_in_5_days',
          ],
        })
        break

      case ESubscriptionType.Professional1Month:
        setContent({
          title: t(plan.name),
          description: t('chatbotplans.professional_description'),
          price: `${plan.price}${currentPrice(plan.curentcy)}`,
          option: [
            'chatbotplans.tokens_per_month',
            'chatbotplans.knowledge_base',
            'chatbotplans.integration_channels',
            'chatbotplans.collect_customer_info_to_gg_sheet',
            'chatbotplans.customer_service_support_within_24h',
            'chatbotplans.complete_implementation_in_5_days',
          ],
        })
        break

      case ESubscriptionType.Bussiness1Month:
        setContent({
          title: t(plan.name),
          description: t('chatbotplans.business_description'),
          price: `${plan.price}${currentPrice(plan.curentcy)}`,
          option: [
            'chatbotplans.tokens_per_month',
            'chatbotplans.knowledge_base',
            'chatbotplans.integration_channels',
            'chatbotplans.collect_customer_info_to_gg_sheet',
            'chatbotplans.evaluate_customer_potential',
            'chatbotplans.customer_service_support_within_24h',
            'chatbotplans.complete_implementation_in_5_days',
          ],
        })
        break

      case ESubscriptionType.Professional1Year:
        setContent({
          title: t(plan.name),
          description: t('chatbotplans.professional_description'),
          price: `${plan.price}${currentPrice(plan.curentcy)}`,
          option: [
            'chatbotplans.tokens_per_month',
            'chatbotplans.knowledge_base',
            'chatbotplans.integration_channels',
            'chatbotplans.collect_customer_info_to_gg_sheet',
            'chatbotplans.customer_service_support_within_24h',
            'chatbotplans.complete_implementation_in_5_days',
          ],
        })
        break

      case ESubscriptionType.Bussiness1Year:
        setContent({
          title: t(plan.name),
          description: t('chatbotplans.business_description'),
          price: `${plan.price}${currentPrice(plan.curentcy)}`,
          option: [
            'chatbotplans.tokens_per_month',
            'chatbotplans.knowledge_base',
            'chatbotplans.integration_channels',
            'chatbotplans.collect_customer_info_to_gg_sheet',
            'chatbotplans.evaluate_customer_potential',
            'chatbotplans.customer_service_support_within_24h',
            'chatbotplans.complete_implementation_in_5_days',
          ],
        })
        break
      case ESubscriptionType.Enterprice:
        setContent({
          title: t(plan.name),
          description: t('chatbotplans.enterprise_description'),
          price: 'Custom',
          option: [
            'chatbotplans.unlimited_tokens_per_month',
            'chatbotplans.negotiated_knowledge_base_capacity',
            'chatbotplans.multi_integration_channels',
            'chatbotplans.advanced_user_profile_collection',
            'chatbotplans.customized_customer_evaluation',
            'chatbotplans.dedicated_customer_support',
            'chatbotplans.negotiated_implementation_time',
          ],
        })
        break
      default:
        break
    }
  }

  const getIcon = (id: string) => {
    switch (id) {
      case ESubscriptionType.Trial1Month:
        return <IconTrial />
      case ESubscriptionType.Professional1Month:
        return <IconProfessional />
      case ESubscriptionType.Bussiness1Month:
        return <IconBusiness />
      case ESubscriptionType.Professional1Year:
        return <IconProfessional />
      case ESubscriptionType.Bussiness1Year:
        return <IconBusiness />
      case ESubscriptionType.Enterprice:
        return <IconEnterprise />
      default:
        break
    }
  }

  useEffect(() => {
    getContent(plan.id)
  }, [plan.id])

  return (
    <div className="flex h-fit w-fit flex-col gap-[12px] rounded-[36px] bg-[#F8F8F8] p-[12px] shadow-xl">
      <div className="flex w-full flex-col gap-[8px] px-[12px]">
        {getIcon(plan.id)}

        <div className="flex flex-col">
          <Text
            type="subheading"
            variant="medium"
            className="text-Primary-Color"
          >
            {content.title}
          </Text>

          <Text
            type="supportText"
            variant="regular"
            className="text-Tertiary-Color"
          >
            {content.description}
          </Text>
        </div>

        <div className="flex items-end gap-[1px]">
          <Text type="title" variant="semibold" className="text-gradient">
            {content.price}
          </Text>
          {content.price !== 'Custom' && (
            <Text
              type="helperText"
              variant="regular"
              className="pb-[6px] text-Secondary-Color"
            >
              /{type === 'Monthly' ? t('common.month') : t('common.year')}
            </Text>
          )}
        </div>
      </div>
      <div className="flex w-full flex-col gap-[20px] rounded-[30px] border border-border-base-icon bg-white px-[12px] pb-[12px] pt-[20px]">
        <div className="flex flex-col justify-center gap-[8px]">
          {content.option.map((item, index) => (
            <div key={index} className="flex items-center gap-[8px]">
              <Icon name="check" color="#199647" size={16} />

              <Text
                type="subBody"
                variant="regular"
                className="text-nowrap text-Secondary-Color"
              >
                <Trans
                  i18nKey={item}
                  values={plan}
                  components={{
                    bold: (
                      <Text
                        type="subBody"
                        variant="regular"
                        className="text-Primary-Color"
                      ></Text>
                    ),
                  }}
                />
              </Text>
            </div>
          ))}
        </div>

        <Button
          type="primary"
          onClick={() => {
            handleContact(plan.name)
          }}
        >
          {t('common.contact')}
        </Button>
      </div>
    </div>
  )
}

export default ItemPlan
